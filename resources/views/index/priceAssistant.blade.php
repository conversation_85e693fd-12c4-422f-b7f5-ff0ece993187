<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>询报价管理后台 | 首页</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    @include('table.css')
    <style>
        .rows {
            display: flex;
            flex-direction: row;
        }

        .panbox {
            height: 80px;
            background: #fff;
            margin-bottom: 20px;
            padding-top: 10px;
            box-sizing: border-box;
            padding-left: 20px;
        }

        .panbox .btnxk {
            background: #1E9FFF;
            padding: 0 40px;
            height: 60px;
            margin-right: 40px;
            color: #fff;
            font-size: 18px;
            text-align: center;
            line-height: 60px;
            border-radius: 3px;
            cursor: pointer;
        }

        .itemheigthauto .layui-table-view .layui-table[lay-size=sm] .layui-table-cell {
            height: auto;
            white-space: pre-line !important;
            word-break: break-all !important;
            word-wrap: break-word !important;
        }

        .itemheigthauto .priceitems {
            width: 80px;
        }

        .priceboxk {
            color: #000;
        }

        .fb {
            font-weight: bold;
        }

        .fcgas {
            color: #1E9FFF;
        }

        .itemgh {
            margin-bottom: 25px;
        }
    </style>
</head>


<body>
    <div id="wrapper">
        <div id="page-wrapper" class="gray-bg">
            <div class="panbox rows">
                <a ew-href="/index/actuals" ew-title="找现货" class="btnxk">找现货</a>
                <a ew-href="/index/agentBuy" ew-title="找代购" class="btnxk">找代购</a>
                <a ew-href="/index/futures" ew-title="找期货" class="btnxk">找期货</a>
                <a ew-href="/index/priceAssistant" ew-title="查费用" class="btnxk">查费用</a>
                <!-- <a ew-href="/index/agency" ew-title="找代理" class="btnxk">找代理</a>
                <a ew-href="/index/bom" ew-title="BOM" class="btnxk">BOM</a> -->
            </div>
            <div class="row">
                <div class="col-lg-12">
                    <div class="ibox float-e-margins ibox-content" style="margin: 15px">
                        <div class="row mapping-rows" style="">
                            <form class="layui-form layui-box" onsubmit="return false" lay-filter="goodsForm">
                                <div class="layui-form-item">
                                    <div class="layui-inline">
                                        <label class="layui-form-label">
                                            <font style="color:red;">*</font>型号
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="text" name="goods_name" value="" autocomplete="off" placeholder="型号" lay-verify="required" class="layui-input">
                                        </div>
                                    </div>
                                    {{--<div class="layui-inline">
                                        <label class="layui-form-label">品牌</label>
                                        <div class="layui-input-inline">
                                            <div id="bn1"></div>
                                            <input type="hidden" id="brand_namess" name="brand_name" class="mhsearchinput" value="">
                                        </div>
                                    </div>--}}
                                    <div class="layui-inline">
                                        <label class="layui-form-label">
                                            <font style="color:red;">*</font>货物数量
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="number" name="goods_number" value="" autocomplete="off" placeholder="货物数量" lay-verify="required" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">
                                            <font style="color:red;">*</font>交货地:
                                        </label>
                                        <div class="layui-input-inline">
                                            <select name="delivery_place" lay-verify="required" lay-filter="delivery_place">
                                                <option value="">全部</option>
                                                <option value="1">大陆</option>
                                                <option value="2">香港</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">
                                            供货渠道:
                                        </label>
                                        <div class="layui-input-inline">
                                            <select name="channel_id" class="channel_id">
                                                <option value="">全部</option>
                                                @foreach ($supplier_map as $supplier_id => $supplier_name)
                                                    <option value="{{$supplier_id}}">{{$supplier_name}}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">
                                            <font style="color:red;">*</font>货值单价:
                                        </label>
                                        <div class="layui-input-inline">
                                            <input type="number" name="goods_price" value="" autocomplete="off" placeholder="货值单价" lay-verify="required" class="layui-input">
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <label class="layui-form-label">币种：</label>
                                        <div class="layui-input-inline">
                                            <div class="rtexthj" style="height:30px;line-height:30px;"></div>
                                        </div>
                                    </div>
                                    <div class="layui-inline">
                                        <div class="layui-btn layui-btn-sm" lay-submit lay-filter="goodsFormLoad">查询
                                        </div>
                                    </div>
                                </div>
                            </form>
                            <div class="priceboxk" id="priceboxk">


                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script type="text/html" id="list_action">
        <a class="layui-btn layui-btn-xs layui-btn-normal" href="javascript:void(0)" lay-event="xj">立即询价</a>
    </script>
    @verbatim
    <script id="tbmpl" type="text/html">
        <br><br>
        <div>根据您给出的信息，查询到该货物可能一下额外费用：</div><br>
        <div class="itemgh">
            <div class="fb">一、预计关税</div><br>
            <div class="">交货地：<span class="fb">{{d.delivery_place}}</span>，预估关税额：<span class="fb fcgas">{{d.tariff_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.tariff_fee_detail}}</div>
        </div>
        <div class="itemgh">
            <div class="fb">二、美国产地税</div><br>
            <div class="">交货地：<span class="fb">{{d.delivery_place}}</span>，预估美国产地税额：<span class="fb fcgas">{{d.land_tax_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.land_tax_fee_detail}}</div>
        </div>
        <div class="itemgh">
            <div class="fb">三、美国对等关税</div><br>
            <div class="">交货地：<span class="fb">{{d.delivery_place}}</span>，预估美国对等关税额：<span class="fb fcgas">{{d.equivalent_tax_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.equivalent_tax_fee_detail}}</div>
        </div>
        <div class="itemgh">
            <div class="fb">四、香港清关费</div><br>
            <div class="">交货地：<span class="fb">{{d.delivery_place}}</span>，预估香港清关费：<span class="fb fcgas">{{d.hk_clearance_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.hk_clearance_fee_detail}}</div>
        </div>
        <div class="itemgh">
            <div class="fb">五、进口商检费用</div><br>
            <div class="">交货地：<span class="fb">{{d.delivery_place}}</span>，预估进口商检费用：<span class="fb fcgas">{{d.import_inspection_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.import_inspection_fee_detail}}</div>
        </div>
        <div class="itemgh">
            <div class="fb">六、供货渠道费</div><br>
            <div class="">供货渠道：<span class="fb">{{d.supplier_name}}</span>，预估渠道费用：<span class="fb fcgas">{{d.supply_channel_fee}} {{d.currency}}</span></div>
            <div style="margin-top:8px;">说明：{{d.supply_channel_fee_detail}}</div>
        </div>

    </script>
@endverbatim

    @include('table.js')
    <script src="/js/web/common.min.js?{{time()}}"></script>
    <script src="/js/index/priceAssistant.js?{{time()}}"></script>
</body>

</html>
