<table>
    <thead>
    <tr>
        <th>BOM单号</th>
        <th>询价客户</th>
        <th>交货地</th>
        <th>询价型号</th>
        <th>标准品牌</th>
        <th>数量</th>
        <th>期待交期</th>
        <th>期待批次</th>
        <th>截止时间</th>
        <th>报价来源</th>
        <th>渠道</th>
        <th>采购员</th>
        <th>报价型号</th>
        <th>标准品牌</th>
        <th>数量</th>
        <th>含税单价</th>
        <th>未税单价</th>
        <th>报价交期</th>
        <th>报价批次</th>
        <th>报价备注</th>
        <th>报价有效时间</th>
    </tr>
    </thead>
    <tbody>
    @foreach($bom_items as $key => $item)
        <tr>
            <td>{{ $bom_info['bom_sn'] ?? '' }}</td>
            <td>{{ $bom_info['customer_user_name'] ?? '' }}</td>
            <td>{{ isset($bom_info['delivery_place']) ? (($bom_info['delivery_place'] == 1) ? '大陆' : '香港') : '' }}</td>
            <td>{{ $item['goods_name'] ?? '' }}</td>
            <td>{{ $item['brand_name'] ?? '' }}</td>
            <td>{{ $item['inquiry_number'] ?? '' }}</td>
            <td>{{ $item['delivery_time'] ?? '' }}</td>
            <td>{{ $item['batch'] ?? '' }}</td>
            <td>{{ isset($item['deadline_time']) ? date('Y/m/d H:i', $item['deadline_time']) : '' }}</td>

            <td>{{ $item['quote_ability_level_cn'] ?? '' }}</td>
            <td>{{ $item['quote_supplier_name'] ?? '' }}</td>
            <td>{{ $item['quote_encoded_name']?? '' }}</td>
            <td>{{ $item['quote_goods_name'] ??'' }}</td>
            <td>{{ $item['quote_brand_name'] ??  '' }}</td>
            <td>{{ $item['quote_number'] ?? '' }}</td>
            <td>{{ $item['quote_price_in_tax'] ?? '' }}</td>
            <td>{{ $item['quote_price_without_tax'] ?? '' }}</td>
            <td>{{ $item['quote_delivery_time'] ?? '' }}</td>
            <td>{{ $item['quote_batch'] ?? '' }}</td>
            <td>{{ $item['quote_remark'] ?? '' }}</td>
            <td>{{ $item['quote_expire_time'] ?? '' }}</td>
        </tr>
    @endforeach
    </tbody>
</table>
