<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>询报价管理后台 | 首页</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    @include('table.css')
    <style>
        body {
            font-size: 12px;
            color: #595959;
        }

        .section-page {
            background-color: #fff;
            margin: 15px;
            padding: 20px;
            border-radius: 3px;
        }

        .layui-form-item .layui-inline {
            vertical-align: middle;
        }

        .layui-form-label {
            padding: 5px 15px !important;
        }

        .section-page .layui-form-label {
            width: 90px;
        }

        .layui-form-label-select {
            text-align: left !important;
            padding: 0 15px !important;
        }
    </style>
</head>

<body>
<!--section-->
<section class="section-page">
    <form class="layui-form" onsubmit="return false" lay-filter="list">
        <div class="layui-form-item" style="margin-bottom: 0;">
            <div class="layui-inline">
                <label class="layui-form-label layui-form-label-select">
                    <select lay-filter="timeChange">
                        <option value="1">创建时间</option>
                    </select>
                </label>
                <div class="layui-input-inline">
                    <input type="text" name="create_time" placeholder="请选择创建时间" autocomplete="off" class="layui-input" id="time"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status">
                        <option value="">全部</option>
                        <option value="1">保存</option>
                        <option value="2">待报价</option>
                        <option value="3">部分报价</option>
                        <option value="4">已报价</option>
                        <option value="5">已取消</option>
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">型号</label>
                <div class="layui-input-inline">
                    <input type="text" name="goods_name" placeholder="请输入型号" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">网站询价单号</label>
                <div class="layui-input-inline">
                    <input type="text" name="web_inquiry_sn" placeholder="请输入网站询价单号" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">询价客户</label>
                <div class="layui-input-inline">
                    <input type="text" name="customer_name" placeholder="请输入询价客户" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">账号</label>
                <div class="layui-input-inline">
                    <input type="text" name="customer_account" placeholder="请输入账号" autocomplete="off" class="layui-input"/>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">销售员</label>
                <div class="layui-input-inline">
                    <div id="create_name"></div>
                </div>
            </div>
            <div class="layui-inline">
                <button class="layui-btn layui-btn-sm" lay-submit lay-filter="getList">查询</button>
                <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm">重置</button>
            </div>
        </div>
    </form>
    <table id="list" lay-filter="list"></table>
</section>
<!--section end-->
<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <a class="layui-btn layui-btn-sm" lay-event="takeWebInquiry">领取客户询价</a>
        <a class="layui-btn layui-btn-sm" lay-event="export">导出</a>
    </div>
</script>
@include('table.js')
<script src="/js/web/common.min.js?v={{time()}}"></script>
<script src="/js/webInquiry/webInquiryBoard.js?v={{time()}}"></script>
</body>

</html>