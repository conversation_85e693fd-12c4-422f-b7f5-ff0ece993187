<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>询报价管理后台 | 首页</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    @include('table.css')
    <style>
        body {
            font-size: 12px;
            color: #595959;
        }

        .row {
            display: flex;
            flex-direction: row;
        }

        .column {
            display: flex;
            flex-direction: column;
        }

        .bothSide {
            justify-content: space-between;
        }

        .avarage {
            justify-content: space-around;
        }

        .rowCenter {
            justify-content: center;
        }

        .verCenter {
            align-items: center;
        }

        .section-page {
            background-color: #fff;
            margin: 15px;
            padding: 20px;
            border-radius: 3px;
        }

        .layui-elem-quote {
            margin-bottom: 10px;
            height: 40px;
            line-height: 40px;
            padding: 0 0 0 10px !important;
            border-left: 5px solid #16b777;
            border-radius: 0 2px 2px 0;
            background-color: #fafafa
        }

        .layui-form-item .layui-inline {
            vertical-align: middle;
        }

        .layui-form-label {
            padding: 5px 15px !important;
        }

        .section-page .layui-form-label {
            width: 85px;
        }

        .show-text {
            line-height: 30px;
            color: #000;
        }

        .confirm-website-inquiry-layer .layui-form-checkbox[lay-skin=primary] {
            padding-left: 0 !important;
        }

        .confirm-website-inquiry-layer .layui-layer-content {
            height: auto !important;
        }

        .confirm-website-inquiry-layer .layui-table th {
            white-space: nowrap;
        }

        .soul-table-child-wrapper {
            padding: 10px;
        }

        .childTable {
            display: none;
        }

        .layui-table td .layui-table-cell {
            cursor: pointer;
        }
    </style>
</head>

<body>
<!--section-->
<section class="section-page">
    <p class="layui-elem-quote">基础信息</p>
    <div class="layui-form-item" id="web_inquiry_info">
        <div class="layui-inline">
            <label class="layui-form-label">网站询价单号：</label>
            <div class="layui-input-inline">
                <span class="show-text web_inquiry_sn"></span>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">询价客户：</label>
            <div class="layui-input-inline">
                <span class="show-text customer_name"></span>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">客户编号：</label>
            <div class="layui-input-inline">
                <span class="show-text user_sn"></span>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">账号：</label>
            <div class="layui-input-inline">
                <span class="show-text customer_account"></span>
            </div>
        </div>
        <div class="layui-inline">
            <label class="layui-form-label">创建时间：</label>
            <div class="layui-input-inline">
                <span class="show-text create_time"></span>
            </div>
        </div>
    </div>
    <p class="layui-elem-quote" style="margin-bottom: 0">商品信息</p>
    <table id="list" lay-filter="list"></table>
    <div class="layui-form-item" style="text-align: center">
        <a class="layui-btn layui-btn-sm layui-btn-primary" onclick="closeCurrentPageJumpOne('网站询价管理','/webInquiry/webInquiryBoard',true)">关闭</a>
    </div>
</section>
<!--section end-->

<!--工具类-->
<script type="text/html" id="toolbar">
    <div class="layui-btn-container">
        <div class="row verCenter">
            <a class="layui-btn layui-btn-sm" lay-event="updateVerifyInquiryInfo">修改客户询价</a>
            <a class="layui-btn layui-btn-sm" lay-event="generateInquiry">生成询价单</a>
            <a class="layui-btn layui-btn-sm" lay-event="confirmItems">网站询价确认</a>
            <div class="row verCenter web_inquiry_info_text" style="margin-left: 20px;font-size: 12px;">
                <span>总型号数：<em class="total_item_count">0</em></span>
                <span style="color: #FD0505;margin-left: 50px;">未推送客户报价：<em class="unconfirmed_count">0</em></span>
                <span style="margin-left: 50px;">已推送客户报价：<em class="confirmed_count">0</em></span>
                <span style="color: #009688;margin-left: 50px;">已下单：<em class="ordered_count">0</em></span>
            </div>
        </div>
    </div>
</script>
<!--修改客户询价-->
<script type="text/html" id="updateVerifyInquiryInfoHtml">
    <div class="layui-form layer-box-padding" lay-filter="updateVerifyInquiryInfoForm">
        <input type="hidden" name="web_inquiry_item_id" value="">
        <div class="layui-row" style="margin-bottom: 20px;">
            <div class="layui-col-md6">
                <h3 style="padding: 10px 0;margin-left: 105px;">客户询价</h3>
            </div>
            <div class="layui-col-md6">
                <h3 style="padding: 10px 0;margin-left: 39px;">修改询价信息</h3>
                <div style="color: #FF9800; margin-bottom: 10px; margin-left: 39px;">默认值为客户询价信息，可人工修改</div>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">型号：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text goods_name">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="goods_name" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请输入型号" lay-vertype="tips"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">标准品牌：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text brand_name">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="brand_name" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请输入标准品牌" lay-vertype="tips"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">数量：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text inquiry_number">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="inquiry_number" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请输入数量" lay-vertype="tips"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">批次：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text batch">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="batch" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请输入批次" lay-vertype="tips"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">交期：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text delivery_time">-</div>
            </div>
            <div class="layui-input-inline" style="width: 90px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="delivery_time_val" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请选择交期" lay-vertype="tips"/>
            </div>
            <div class="layui-input-inline" style="width: 100px;">
                <select name="delivery_time_unit" class="selectNore delivery_time_unitx">
                    <option value="工作日">工作日</option>
                    <option value="周">周</option>
                </select>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">截止时间：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text deadline_time">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px; position: relative;">
                <span style="position: absolute; left: -7px; top: 0px; color: red;">*</span>
                <input type="text" name="deadline_time" value="" autocomplete="off" class="layui-input" lay-verify="required" lay-reqtext="请选择截止时间" lay-vertype="tips"/>
            </div>
        </div>
        <div class="layui-form-item">
            <label class="layui-form-label">备注：</label>
            <div class="layui-input-inline" style="width: 200px;">
                <div class="show-text remark">-</div>
            </div>
            <div class="layui-input-inline" style="width: 200px;">
                <input type="text" name="remark" value="" autocomplete="off" class="layui-input"/>
            </div>
        </div>
        <div class="layui-form-item" style="text-align: center; margin-top: 20px;">
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="layer.closeAll()">返回</button>
            <button type="submit" class="layui-btn layui-btn-sm" lay-submit lay-filter="updateVerifyInquiryInfoSubmit">保存</button>
            <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="generateInquirySubmit">生成询价单</button>
        </div>
    </div>
</script>
<!--网站询价确认-->
<script type="text/html" id="confirmItemsHtml">
    <div class="layui-form layer-box-padding" lay-filter="confirmItemsForm">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label>网站询价单号：<em class="web_inquiry_sn"></em></label>
                <label style="margin-left: 20px;">总型号数：<em class="total_item_count">0</em></label>
                <label style="margin-left: 20px;">采购已报价：<em class="quoted_count">0</em></label>
                <label style="margin-left: 20px;">已推送客户：<em class="puched_count">0</em></label>
            </div>
        </div>
        <div class="data-list" style="max-height: 500px;overflow-y: visible"></div>
        <div style="color: #FD0505; margin: 10px 0;">推送客户后，客户将可按销售确认的信息进行下单购买</div>
        <div class="layui-form-item" style="text-align: center; margin-top: 20px;">
            <button type="button" class="layui-btn layui-btn-sm layui-btn-primary" onclick="layer.closeAll()">返回</button>
            <!-- <button type="submit" class="layui-btn layui-btn-sm" lay-submit lay-filter="confirmItemsSubmit" data-is-push-customer="0">保存</button> -->
            <button type="button" class="layui-btn layui-btn-sm" lay-submit lay-filter="confirmItemsSubmit" data-is-push-customer="1">确认推送客户</button>
        </div>
    </div>
</script>
@verbatim
    <!--网站询价确认-列表数据-->
    <script type="text/html" id="listHtml">
        <table class="layui-table" lay-size="sm">
            <thead>
            <tr>
                <th style="width: 50px;text-align: center">序号</th>
                <th style="width: 60px;"></th>
                <th>型号</th>
                <th>标准品牌</th>
                <th>数量</th>
                <th>含税单价</th>
                <th>批次</th>
                <th>交期</th>
                <th>备注</th>
                <th>有效时间</th>
                <th>状态</th>
            </tr>
            </thead>
            <tbody>
            {{# layui.each(d, function(index, item){ }}
            <tr style="background-color: #e8f7fd;">
                <td rowspan="3" style="text-align: center;background: #fff;">
                    <div class="column rowCenter verCenter">
                        <span>{{index+1}}</span>
                        <input type="checkbox" name="check" lay-skin="primary" lay-filter="checkAll" title="" value="1">
                    </div>
                </td>
                <td>网站询价</td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.goods_name || ''}}</td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.brand_name || ''}}</td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.inquiry_number || ''}}</td>
                <td style="white-space: nowrap;">
                    <div class="rows bothSide verCenter">
                        <span>{{item.web_inquiry_item_info && item.web_inquiry_item_info.web_show_price || ''}}</span>
                        <span style="color: #339CE9;">{{item.web_inquiry_item_info && item.web_inquiry_item_info.currency_val || ''}}</span>
                    </div>
                </td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.batch || ''}}</td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.delivery_time || ''}}</td>
                <td>{{item.web_inquiry_item_info && item.web_inquiry_item_info.remark || ''}}</td>
                <td></td>
                <td style="white-space: nowrap;">{{item.web_inquiry_item_info && item.web_inquiry_item_info.status_val || ''}}</td>
            </tr>
            <tr style="background-color: #f2f9f5;">
                <td>采购报价</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.goods_name || ''}}</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.brand_name || ''}}</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.quote_number || ''}}</td>
                <td style="white-space: nowrap;">
                    <div class="rows bothSide verCenter">
                        <span>{{item.buyer_quote_info && item.buyer_quote_info.quote_price || ''}}</span>
                        <span style="color: #339CE9;">{{item.buyer_quote_info && item.buyer_quote_info.currency_val || ''}}</span>
                    </div>
                </td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.batch || ''}}</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.delivery_time || ''}}</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.remark || ''}}</td>
                <td>{{item.buyer_quote_info && item.buyer_quote_info.expire_time || ''}}</td>
                <td style="white-space: nowrap;">{{item.buyer_quote_info && item.buyer_quote_info.status_val || ''}}</td>
            </tr>
            <tr>
                <td>销售确认</td>
                <td>
                    <input type="hidden" class="web_inquiry_item_id" value="{{item.web_inquiry_item_info && item.web_inquiry_item_info.web_inquiry_item_id || ''}}">
                    <input type="hidden" class="quote_id" value="{{item.buyer_quote_info && item.buyer_quote_info.quote_id || ''}}">
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.goods_name || ''}}" autocomplete="off" class="layui-input goods_name" lay-verify="required" lay-reqtext="请输入型号" lay-vertype="tips"/>
                </td>
                <td>
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.brand_name || ''}}" autocomplete="off" class="layui-input brand_name" lay-verify="required" lay-reqtext="请输入标准品牌" lay-vertype="tips"/>
                </td>
                <td>
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.quote_number || ''}}" autocomplete="off" class="layui-input quote_number" lay-verify="required" lay-reqtext="请输入数量" lay-vertype="tips"/>
                </td>
                <td>
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <div class="row bothSide verCenter" style="white-space: nowrap">
                        <input type="hidden" class="currency" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.currency || ''}}">
                        <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.quote_price || ''}}" class="layui-input goods_price" style="width: 65px;" lay-verify="required" lay-reqtext="请输入数量" lay-vertype="tips">
                        <span style="color: #339CE9;margin-left: 5px">{{item.sale_confirmed_quote && item.sale_confirmed_quote.currency_val || ''}}</span>
                    </div>
                </td>
                <td>
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.batch || ''}}" autocomplete="off" class="layui-input batch" lay-verify="required" lay-reqtext="请输入批次" lay-vertype="tips"/>
                </td>
                <td>
                    <span style="position: absolute; left: 4px; top: 0px; color: red;">*</span>
                    <div class="row verCenter">
                        <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.delivery_info.delivery_int || ''}}" class="layui-input delivery_time_val" style="width: 50px;border-right: none;" lay-verify="required" lay-reqtext="请输入交期" lay-vertype="tips">
                        <select class="delivery_time_unit">
                            <option value="工作日" {{# if(item.sale_confirmed_quote && item.sale_confirmed_quote.delivery_info.delivery_unit_val=='工作日'){ }}selected{{# } }}>工作日</option>
                            <option value="周" {{# if(item.sale_confirmed_quote && item.sale_confirmed_quote.delivery_info.delivery_unit_val=='周'){ }}selected{{# } }}>周</option>
                        </select>
                    </div>
                </td>
                <td>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.remark || ''}}" class="layui-input remark">
                </td>
                <td>
                    <input type="text" value="{{item.sale_confirmed_quote && item.sale_confirmed_quote.expire_time || ''}}" class="layui-input expire_time">
                </td>
                <td style="white-space: nowrap;">{{item.sale_confirmed_quote && item.sale_confirmed_quote.status_val || ''}}</td>
            </tr>
            {{# }); }}
            </tbody>
        </table>
    </script>
@endverbatim

<script type="text/javascript" src="/assets/libs/jquery/jquery-3.2.1.min.js"></script>
<script type="text/javascript" src="/assets/libs/layui/layui.js?v=1"></script>
<script type="text/javascript" src="/assets/js/common.js?v=1"></script>
<script src="/js/webInquiry/webInquiryDetail.js?v={{time()}}"></script>
</body>

</html>