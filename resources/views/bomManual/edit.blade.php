<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>编辑BOM</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    <style>
        .edit-container {
            padding: 20px;
            background: #fff;
            margin: 15px;
            border-radius: 5px;
        }
        .layui-form-label {
            width: 120px;
        }
        .layui-input-block {
            margin-left: 150px;
        }
    </style>
</head>

<body>
<div id="wrapper">
    <div id="page-wrapper" class="gray-bg">
        <div class="edit-container">
            <h2>编辑BOM信息</h2>
            
            <form class="layui-form" lay-filter="editForm">
                <input type="hidden" name="id" value="{{ $bom_info['id'] }}">
                
                <div class="layui-form-item">
                    <label class="layui-form-label">BOM编号</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_sn" value="{{ $bom_info['bom_sn'] }}" readonly class="layui-input layui-disabled">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">BOM名称</label>
                    <div class="layui-input-block">
                        <input type="text" name="bom_title" value="{{ $bom_info['bom_title'] }}" lay-verify="required" placeholder="请输入BOM名称" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">状态</label>
                    <div class="layui-input-block">
                        <select name="status" lay-verify="required">
                            <option value="1" {{ $bom_info['status'] == 1 ? 'selected' : '' }}>未匹配</option>
                            <option value="2" {{ $bom_info['status'] == 2 ? 'selected' : '' }}>已匹配</option>
                            <option value="3" {{ $bom_info['status'] == 3 ? 'selected' : '' }}>部分匹配</option>
                        </select>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">指定采购员ID</label>
                    <div class="layui-input-block">
                        <input type="number" name="assigned_user_id" value="{{ $bom_info['assigned_user_id'] }}" placeholder="请输入采购员ID" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">指定采购员姓名</label>
                    <div class="layui-input-block">
                        <input type="text" name="assigned_user_name" value="{{ $bom_info['assigned_user_name'] }}" placeholder="请输入采购员姓名" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <label class="layui-form-label">源文件路径</label>
                    <div class="layui-input-block">
                        <input type="text" name="source_file_path" value="{{ $bom_info['source_file_path'] }}" placeholder="请输入源文件路径" autocomplete="off" class="layui-input">
                    </div>
                </div>
                
                <div class="layui-form-item layui-form-text">
                    <label class="layui-form-label">备注</label>
                    <div class="layui-input-block">
                        <textarea name="bom_remark" placeholder="请输入备注信息" class="layui-textarea">{{ $bom_info['bom_remark'] }}</textarea>
                    </div>
                </div>
                
                <div class="layui-form-item">
                    <div class="layui-input-block">
                        <button class="layui-btn" lay-submit lay-filter="submitForm">保存</button>
                        <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">取消</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="/js/web/layui/layui.js"></script>
<script>
layui.use(['form', 'layer'], function(){
    var form = layui.form;
    var layer = layui.layer;
    
    // 监听提交
    form.on('submit(submitForm)', function(data){
        var formData = data.field;
        
        $.ajax({
            url: '/api/bomManual/updateBomManual',
            type: 'post',
            data: formData,
            dataType: 'json',
            success: function (res) {
                if (res.code == 200) {
                    layer.msg('保存成功', {icon: 1}, function(){
                        parent.layer.closeAll();
                    });
                } else {
                    layer.msg(res.msg || '保存失败', {icon: 2});
                }
            },
            error: function () {
                layer.msg('网络错误，请重试', {icon: 5});
            }
        });
        
        return false;
    });
});
</script>

</body>

</html>
