<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>BOM人工处理列表</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    @include('table.css')
    <style>
        td[data-field="1"] .laytable-cell {
            overflow: visible;
        }

        .layui-table-cell {
            overflow: hidden;
        }

        .layui-table-view .layui-table td {
            cursor: pointer;
        }

        tr.actx {
            background-color: #f2f2f2;
        }

        input.edshow {
            height: 28px;
            line-height: 28px;
        }

        .edshow select,
        .edshow input {
            height: 28px;
            line-height: 28px;
        }

        .layui-form-label {
            width: 100px;
        }

        .zdyui .layui-form-label {
            width: auto;
            max-width: 80px;
            padding-left: 0px;
        }

        .btncen {
            position: absolute;
            width: 80px;
            height: 50px;
            background: #fff;
            top: 9999px;
            left: 9999px;
            box-shadow: 0px 0px 2px #ccc;
        }

        .btncen a {
            display: inline-block;
            width: 100%;
            height: 25px;
            line-height: 25px;
            color: #333;
            font-size: 12px;
            cursor: pointer;
            padding-left: 10px;
        }

        .btncen a:hover {
            color: #1969F9;
        }
    </style>

</head>

<body>
<div id="wrapper">
    <div id="page-wrapper" class="gray-bg">
        <div class="ibox float-e-margins ibox-content" style="margin: 15px">
            <div class="row mapping-rows" style="overflow:hidden;margin: 10px;margin-top:0px;">
                <form class="layui-form layui-box zdyui" method="post" οnsubmit="return false">
                    <div class="layui-form-item" style="margin-bottom: -4px;">
                        <div class="layui-inline">
                            <label class="layui-form-label">创建时间</label>
                            <div class="layui-input-inline">
                                <input type="text" name="search_date" value="" autocomplete="off" placeholder="选择时间" class="layui-input" id="search_date" readonly>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-inline">
                                <select name="status">
                                    <option value="">全部</option>
                                    <option value="1">未匹配</option>
                                    <option value="2">已匹配</option>
                                    <option value="3">部分匹配</option>
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">BOM编号</label>
                            <div class="layui-input-inline">
                                <input type="text" name="bom_sn" placeholder="请输入BOM编号" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">BOM名称</label>
                            <div class="layui-input-inline">
                                <input type="text" name="bom_title" placeholder="请输入BOM名称" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                    </div>
                    <div class="layui-form-item" style="margin-bottom: -4px;">
                        <div class="layui-inline">
                            <label class="layui-form-label">指定采购员</label>
                            <div class="layui-input-inline">
                                <input type="text" name="assigned_user_name" placeholder="请输入采购员姓名" autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">创建人</label>
                            <div class="layui-input-inline">
                                <div id="user"></div>
                                <input type="hidden" id="create_name" name="create_name" class="mhsearchinput" value="">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <div class="layui-btn layui-btn-sm" lay-submit lay-filter="load">查询</div>
                            <button type="reset" class="layui-btn layui-btn-primary layui-btn-sm czbtn">重置</button>
                        </div>
                    </div>
                </form>
                <table id="list" lay-filter="list"></table>
            </div>
        </div>
    </div>
</div>
<div class="btncen">
    <a class="pdfbtng">导出pdf</a>
    <a class="excelbtng">导出excel</a>
</div>
<script type="text/html" id="toorBar">
    <a class="layui-btn layui-btn-xs" lay-event="detail">详情</a>
    <a class="layui-btn layui-btn-xs" lay-event="edit">编辑</a>
    <a class="layui-btn layui-btn-xs layui-btn-danger" lay-event="delete">删除</a>
</script>
@include('table.js')
<script src="/js/web/log.js?{{time()}}"></script>
<script src="/js/bomManual/bomManualList.js?{{time()}}"></script>

</body>

</html>
