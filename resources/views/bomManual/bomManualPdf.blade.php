<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>BOM详情</title>
    <style>
        body {
            font-family: "SimSun", serif;
            font-size: 12px;
            margin: 0;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .info-table td {
            border: 1px solid #ddd;
            padding: 8px;
            vertical-align: top;
        }
        .info-table .label {
            background-color: #f5f5f5;
            font-weight: bold;
            width: 120px;
        }
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            color: #fff;
            font-size: 11px;
        }
        .status-1 { background-color: #ff9800; }
        .status-2 { background-color: #4caf50; }
        .status-3 { background-color: #2196f3; }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">BOM人工处理详情</div>
        <div>导出时间：{{ date('Y-m-d H:i:s') }}</div>
    </div>

    <table class="info-table">
        <tr>
            <td class="label">BOM编号</td>
            <td>{{ $bom_info['bom_sn'] }}</td>
            <td class="label">BOM名称</td>
            <td>{{ $bom_info['bom_title'] }}</td>
        </tr>
        <tr>
            <td class="label">状态</td>
            <td>
                <span class="status-badge status-{{ $bom_info['status'] }}">{{ $bom_info['status_name'] }}</span>
            </td>
            <td class="label">指定采购员</td>
            <td>{{ $bom_info['assigned_user_name'] ?: '未指定' }}</td>
        </tr>
        <tr>
            <td class="label">源文件路径</td>
            <td colspan="3">{{ $bom_info['source_file_path'] ?: '无' }}</td>
        </tr>
        <tr>
            <td class="label">备注</td>
            <td colspan="3">{{ $bom_info['bom_remark'] ?: '无' }}</td>
        </tr>
        <tr>
            <td class="label">创建人</td>
            <td>{{ $bom_info['create_name'] }}</td>
            <td class="label">创建时间</td>
            <td>{{ $bom_info['create_time'] }}</td>
        </tr>
        <tr>
            <td class="label">更新人</td>
            <td>{{ $bom_info['update_name'] }}</td>
            <td class="label">更新时间</td>
            <td>{{ $bom_info['update_time'] }}</td>
        </tr>
    </table>

    <div class="footer">
        <div>系统生成时间：{{ date('Y-m-d H:i:s') }}</div>
    </div>
</body>
</html>
