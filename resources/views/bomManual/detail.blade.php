<!doctype html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="ie=edge">
    <title>BOM详情</title>
    <link href="/js/web/layui/css/layui.css" rel="stylesheet">
    <link href="/css/common.min.css" rel="stylesheet">
    <style>
        .detail-container {
            padding: 20px;
            background: #fff;
            margin: 15px;
            border-radius: 5px;
        }
        .detail-item {
            margin-bottom: 15px;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .detail-label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
            color: #333;
        }
        .detail-value {
            color: #666;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 3px;
            color: #fff;
            font-size: 12px;
        }
        .status-1 { background-color: #ff9800; }
        .status-2 { background-color: #4caf50; }
        .status-3 { background-color: #2196f3; }
    </style>
</head>

<body>
<div id="wrapper">
    <div id="page-wrapper" class="gray-bg">
        <div class="detail-container">
            <h2>BOM详情信息</h2>
            
            <div class="detail-item">
                <span class="detail-label">BOM编号：</span>
                <span class="detail-value">{{ $bom_info['bom_sn'] }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">BOM名称：</span>
                <span class="detail-value">{{ $bom_info['bom_title'] }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">状态：</span>
                <span class="detail-value">
                    <span class="status-badge status-{{ $bom_info['status'] }}">{{ $bom_info['status_name'] }}</span>
                </span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">指定采购员：</span>
                <span class="detail-value">{{ $bom_info['assigned_user_name'] ?: '未指定' }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">源文件路径：</span>
                <span class="detail-value">{{ $bom_info['source_file_path'] ?: '无' }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">备注：</span>
                <span class="detail-value">{{ $bom_info['bom_remark'] ?: '无' }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">创建人：</span>
                <span class="detail-value">{{ $bom_info['create_name'] }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">创建时间：</span>
                <span class="detail-value">{{ $bom_info['create_time'] }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">更新人：</span>
                <span class="detail-value">{{ $bom_info['update_name'] }}</span>
            </div>
            
            <div class="detail-item">
                <span class="detail-label">更新时间：</span>
                <span class="detail-value">{{ $bom_info['update_time'] }}</span>
            </div>
            
            <div style="margin-top: 30px;">
                <button type="button" class="layui-btn" onclick="editBom()">编辑</button>
                <button type="button" class="layui-btn layui-btn-primary" onclick="parent.layer.closeAll()">关闭</button>
            </div>
        </div>
    </div>
</div>

<script src="/js/web/layui/layui.js"></script>
<script>
layui.use(['layer'], function(){
    var layer = layui.layer;
    
    window.editBom = function() {
        parent.layer.open({
            type: 2,
            title: '编辑BOM',
            shadeClose: true,
            shade: 0.8,
            area: ['60%', '70%'],
            content: '/bomManual/edit?id={{ $bom_info["id"] }}',
            end: function(){
                parent.location.reload();
            }
        });
    }
});
</script>

</body>

</html>
