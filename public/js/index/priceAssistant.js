layui.use(['form', 'layer', 'laydate', 'upload', 'table', 'laydate', 'index','xmSelect','laytpl'], function () {
    var table = layui.table;
    var form = layui.form;
    var index = layui.index;
    var xmSelect=layui.xmSelect
    var laytpl=layui.laytpl
    var arrinit = []
  
 
  
  
    //品牌搜索
    var bn1s = xmSelect.render({
        el: '#bn1',
        autoRow: false,
        radio: true,
        toolbar: {show: false},
        remoteSearch: true,
        filterable: true,
        clickClose: true,
        data: [],

        remoteMethod: function (val, cb, show) {
            //这里如果val为空, 则不触发搜索
            if (!val) return cb([]);

            $.ajax({
                url: '/api/search/getBrand',
                type: 'get',
                data: {
                    brand: val,
                },
                dataType: 'json',
                timeout: 1000,
                success: function (res) {
                    var a = [];
                    var arr_ = res.data.list;
                    for (var i = 0; i < arr_.length; i++) {
                        a.push({
                            name: arr_[i],
                            value: arr_[i]
                        })
                    }
                    cb(a)
                },
                error: function () {
                    return layer.msg('网络错误，请重试', {icon: 5});
                }
            });
        }
        , on: function (data) {
            var arr = data.arr
            if (arr.length == 0) {
                $("#brand_namess").val("");
            } else {
                var json = [];
                var j = {};
                j.value = data.arr[0].value;
                j.name = data.arr[0].name;
                json.push(j);
                var brand = json;
                $("#brand_namess").val(brand[0].value);
            }
        }
    })

    
    form.on('select(delivery_place)', function (data) {
        console.log(data.value)
        if(data.value==1){
            $(".rtexthj").text("人民币")
        }else if(data.value==2){
            $(".rtexthj").text("美元")
        }else{
            $(".rtexthj").text("")
        }
    })
    //提交
    form.on('submit(goodsFormLoad)', function (data) {
      console.log(data.field)
      var obj_=data.field
      Request('/api/priceAssistant/getGoodsExtraCharge', 'post',obj_, function (res) {
        if (res.code == 0) {
            $(".priceboxk").html("")
            var infos_=res.data.extra_charge_info
            infos_.delivery_place=obj_.delivery_place==2?'香港':'大陆'
            infos_.currency=obj_.delivery_place==2?'USD':'RMB'
            infos_.supplier_name=$(".channel_id option:selected").text()
            console.log(infos_)
            laytpl($("#tbmpl").html()).render(infos_, function (html) {
                $(".priceboxk").append(html)
              });
              
        } else {
            layer.msg(res.msg);
        }
    });
    })
  
  
  })
  