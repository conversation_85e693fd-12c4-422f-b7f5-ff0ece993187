layui.extend({
  soulTable: 'soulTable/soulTable',
  tableChild: 'soulTable/tableChild',
  tableMerge: 'soulTable/tableMerge',
  tableFilter: 'soulTable/tableFilter',
  excel: 'soulTable/excel'
}).use(['admin', 'index', 'form', 'table', 'laydate', 'soulTable'], function () {
  var admin = layui.admin;
  var index = layui.index;
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;
  var soulTable = layui.soulTable;
  var web_inquiry_id = getQueryVariable('web_inquiry_id');

  window.IndexController = {
    init: function () {
      this.created(this).render(this).handleBind(this);
    },
    created: function () {

      return this;
    },
    render: function () {

      //列表渲染
      table.render({
        elem: '#list',
        url: '/api/webInquiry/getWebInquiryItemList',
        toolbar: '#toolbar',
        method: 'POST',
        page: true,
        size: 'sm',
        drag: false,
        cellMinWidth: 80,
        defaultToolbar: ['filter'],
        limit: 10,
        limits: [10, 50, 100, 200],
        where: {
          web_inquiry_id: web_inquiry_id
        },
        cols: [[
          {type: 'checkbox', width: 50, align: 'center', fixed: 'left'},
          {type: 'numbers', title: '序号', width: 50, align: 'center', fixed: 'left'},
          {
            field: 'goods_name', title: '型号', width: 120, align: 'left', childTitle: false, children: function (row) {
              return [{
                elem: "#list" + row.web_inquiry_id,
                className: 'childClass' + row.web_inquiry_id,
                data: row.sale_confirmed_quote,
                page: false,
                drag: false,
                cols: [[
                  {field: 'goods_name', title: '销售确认型号', width: 130, align: 'center'},
                  {field: 'brand_name', title: '确认品牌', width: 130, align: 'center'},
                  {field: 'quote_number', title: '确认数量', width: 120, align: 'center'},
                  {field: 'quote_price', title: '销售确认单价', width: 120, align: 'center'},
                  {field: 'currency_val', title: '币种', width: 100, align: 'center'},
                  {field: 'delivery_time', title: '交期', width: 100, align: 'center'},
                  {field: 'batch', title: '批次', width: 100, align: 'center'},
                  {field: 'remark', title: '备注', minWidth: 150, align: 'center'},
                  {field: 'expire_time', title: '价格有效时间', width: 150, align: 'center'},
                  {field: 'create_time', title: '确认时间', width: 150, align: 'center'}
                ]],
                parseData: LayUiTableParseData,
                done: function (res, curr, count) {
                  soulTable.render(this);
                }
              }]
            }
          },
          {field: 'brand_name', title: '标准品牌', width: 120, align: 'left'},
          {field: 'inquiry_number', title: '数量', width: 80, align: 'left'},
          {field: 'web_show_price', title: '网站展示单价', width: 113, align: 'left'},
          {field: 'expect_price', title: '期望单价', width: 100, align: 'left'},
          {field: 'sale_confirmed_price', title: '销售确认单价', width: 110, align: 'left'},
          {field: 'currency_val', title: '币种', width: 80, align: 'left'},
          {field: 'delivery_time', title: '期待交期', width: 100, align: 'left'},
          {field: 'batch', title: '批次', width: 80, align: 'left'},
          {field: 'special_request', title: '特殊要求', width: 100, align: 'left'},
          {field: 'item_status_val', title: '推送客户报价', width: 109, align: 'left'},
          {
            field: 'rela_inquiry_sn', title: '询价单号', width: 133, align: 'left', templet(d) {
              return `<a class="alink" ew-href="/inquiry/inquiryBoard?inquiry_sn=${d.rela_inquiry_sn}" ew-title="询价管理">${d.rela_inquiry_sn}</a>`
            }
          },
          {field: 'rela_inquiry_stauts_val', title: '询价单状态', width: 100, align: 'left'},
          {field: 'rela_order_sn', title: '关联销售订单', width: 150, align: 'left'},
          {field: 'verify_goods_name', title: '修改后询价型号', width: 150, align: 'left'},
          {field: 'verify_brand_name', title: '修改后询价品牌', width: 150, align: 'left'},
          {field: 'verify_inquiry_number', title: '修改后询价数量', width: 150, align: 'left'},
          {field: 'verify_batch', title: '修改后询价批次', width: 150, align: 'left'},
          {field: 'verify_delivery_time', title: '修改后询价交期', width: 150, align: 'left'},
          {field: 'verify_deadline_time', title: '修改后询价截止时间', width: 150, align: 'left'},
          {field: 'verify_remark', title: '修改后备注', width: 100, align: 'left'},
          {field: 'sku_id', title: '原询价SKU', width: 160, align: 'left'}
        ]],
        parseData: LayUiTableParseData,
        done: function (res, curr, count) {
          IndexController.getWebInquiryDetail();
          soulTable.render(this);
          layui.form.render();
        }
      });

      //监听头工具栏事件
      table.on('toolbar(list)', function (obj) {
        var checkStatus = table.checkStatus(obj.config.id);
        var data = checkStatus.data;

        switch (obj.event) {
          //修改客户询价
          case 'updateVerifyInquiryInfo':
            if (data.length != 1) {
              layer.msg('请勾选一条数据');
              return;
            }
            // 检查是否有询价单号，如果有则不能修改
            if (data[0].rela_inquiry_sn) {
              layer.msg('该询价已生成询价单，不能修改');
              return;
            }
            layer.open({
              type: 1,
              title: '修改客户询价',
              offset: '50px',
              area: ['600px', 'auto'],
              shadeClose: false,
              resize: false,
              move: false,
              content: $('#updateVerifyInquiryInfoHtml').html(),
              success: function (layero, dIndex) {
                layero.find('.layui-layer-content').css('overflow', 'visible');
                //时间初始化
                laydate.render({
                  elem: layero.find("input[name='deadline_time']")[0],
                  type: 'datetime'
                });
                // 定义需要更新的字段映射
                var fields = ['goods_name', 'brand_name', 'inquiry_number', 'batch', 'delivery_time', 'deadline_time', 'remark'];
                fields.forEach(field => {
                  // 判断data[0][field]有值才赋值
                  if (data[0][field]) {
                    $(layero).find(`.${field}`).text(data[0][field]);
                  }
                });
                layui.form.val('updateVerifyInquiryInfoForm', {
                  web_inquiry_item_id: data[0].id,
                  goods_name: data[0].verify_goods_name || data[0].goods_name,
                  brand_name: data[0].verify_brand_name || data[0].brand_name,
                  inquiry_number: data[0].verify_inquiry_number || data[0].inquiry_number,
                  batch: data[0].verify_batch || data[0].batch,
                  delivery_time_val: data[0].verify_delivery_info.delivery_unit || data[0].delivery_info.delivery_unit,
                  delivery_time_unit: data[0].verify_delivery_info.delivery_unit_val || data[0].delivery_info.delivery_unit_val,
                  deadline_time: data[0].verify_deadline_time || data[0].deadline_time,
                  remark: data[0].verify_remark || data[0].remark
                });
                // 初始化品牌自动完成
                IndexController.initBrandAutocomplete(layero.find("input[name='brand_name']"));
                layui.form.render();
              }
            });
            break;
          //生成询价单
          case 'generateInquiry':
            if (data.length == 0) {
              layer.msg('请至少勾选一条数据');
              return;
            }
            var web_inquiry_item_ids = data.map(item => item.id);
            Request('/api/webInquiry/generateInquiry', 'POST', {web_inquiry_item_ids: web_inquiry_item_ids.join(',')}, function (res) {
              if (res.code == 0) {
                layer.msg(res.msg, {shift: 0, time: 2000}, function () {
                  layer.closeAll();
                  table.reload('list');
                });
              } else {
                layer.msg(res.msg);
              }
            });
            break;
          //网站询价确认
          case 'confirmItems':
            if (data.length == 0) {
              layer.msg('请至少勾选一条数据');
              return;
            }
            //必须要勾选有询价单号rela_inquiry_sn，且询价单状态为已报价rela_inquiry_stauts=2
            var invalidItems = data.filter(item => !item.rela_inquiry_sn || item.rela_inquiry_stauts != 2);
            if (invalidItems.length > 0) {
              layer.msg('请勾选有询价单号且询价单状态为已报价的数据');
              return;
            }
            layer.open({
              type: 1,
              title: '网站询价确认',
              offset: '50px',
              area: ['1200px', 'auto'],
              shadeClose: false,
              resize: false,
              move: false,
              skin: 'confirm-website-inquiry-layer',
              content: $('#confirmItemsHtml').html(),
              success: function (layero, dIndex) {
                layero.find('.layui-layer-content').css('overflow', 'visible');
                var web_inquiry_item_ids = data.map(item => item.id);
                Request('/api/webInquiry/getConfirmItemList', 'POST', {web_inquiry_item_ids: web_inquiry_item_ids.join(',')}, function (res) {
                  if (res.code == 0) {
                    var data = res.data.list;
                    var tpl = listHtml.innerHTML;
                    layui.laytpl(tpl).render(data, function (html) {
                      $(layero).find(".data-list").empty().html(html);
                      layui.form.render();
                    });
                    // 定义需要更新的字段映射
                    var fields = ['web_inquiry_sn', 'total_item_count', 'quoted_count', 'puched_count'];
                    fields.forEach(field => {
                      // 判断res.data.web_inquiry_info[field]有值才赋值
                      if (res.data.web_inquiry_info[field]) {
                        $(layero).find(`.${field}`).empty().text(res.data.web_inquiry_info[field]);
                      }
                    });
                    // 初始化品牌自动完成
                    layero.find(".brand_name").each(function () {
                      laydate.render({
                        elem: $(this).parent().parent().find(".expire_time")[0],
                        type: 'datetime'
                      });
                      IndexController.initBrandAutocomplete($(this));
                    });
                    layui.form.render();
                  } else {
                    layer.msg(res.msg);
                  }
                });
              }
            });
            break;
        }
      });

      //行单击事件
      table.on('row(list)', function (obj) {
        var index = obj.index;
        var tr = obj.tr;
        tr.eq(0).find('.childTable').trigger('click');
      });

      return this;
    },
    /**
     * 获取网站询价详情
     */
    getWebInquiryDetail: function () {
      Request('/api/webInquiry/getWebInquiryDetail', 'POST', {web_inquiry_id: web_inquiry_id}, function (res) {
        if (res.code == 0) {
          var data = res.data.web_inquiry_info;
          // 定义需要更新的字段映射
          var fields = ['web_inquiry_sn', 'customer_name', 'user_sn', 'customer_account', 'create_time', 'total_item_count', 'unconfirmed_count', 'confirmed_count', 'ordered_count'];
          fields.forEach(field => {
            // 判断data[field]有值才赋值
            if (data[field]) {
              $(".section-page").find(`.${field}`).text(data[field]);
            }
          });
        } else {
          layer.msg(res.msg);
        }
      });
    },
    /**
     * 初始化品牌自动完成功能
     * @param {Object} inputElem - 品牌输入框元素
     */
    initBrandAutocomplete: function (inputElem) {
      // 创建下拉容器
      var dropdownId = 'brand-dropdown-' + Math.floor(Math.random() * 1000);
      var dropdownHtml = '<div id="' + dropdownId + '" class="brand-dropdown" style="position:absolute;z-index:999;background:#fff;border:1px solid #e6e6e6;max-height:400px;overflow-y:auto;width:100%;box-sizing:border-box;display:none;"></div>';

      // 添加下拉容器到DOM
      inputElem.parent().css('position', 'relative');
      inputElem.after(dropdownHtml);
      var dropdownElem = $('#' + dropdownId);

      // 高亮匹配的文字
      function highlightMatch(text, query) {
        // 忽略大小写
        var index = text.toLowerCase().indexOf(query.toLowerCase());
        if (index >= 0) {
          var before = text.substring(0, index);
          var match = text.substring(index, index + query.length);
          var after = text.substring(index + query.length);
          return before + '<span style="color:#FF0000;">' + match + '</span>' + after;
        }
        return text;
      }

      // 搜索品牌的函数
      function searchBrand(value) {
        // 如果输入为空，隐藏下拉框
        if (!value) {
          dropdownElem.hide().empty();
          return;
        }

        // 请求接口获取品牌数据
        Request('/api/search/getBrand', 'GET', {brand: value}, function (res) {
          if (res.code == 0 && res.data.list && res.data.list.length > 0) {
            // 渲染下拉列表
            var html = '';
            res.data.list.forEach(function (brand) {
              // 高亮匹配的文字部分
              var highlightedBrand = highlightMatch(brand, value);
              html += '<div class="brand-item" style="padding:5px 10px;cursor:pointer;hover:background-color:#f2f2f2;">' + highlightedBrand + '</div>';
            });

            dropdownElem.html(html).show();

            // 点击选择品牌
            dropdownElem.find('.brand-item').on('click', function () {
              var selectedBrand = $(this).text();
              inputElem.val(selectedBrand);
              dropdownElem.hide();
            });

            // 鼠标悬停效果
            dropdownElem.find('.brand-item').hover(
              function () { $(this).css('background-color', '#f2f2f2'); },
              function () { $(this).css('background-color', '#fff'); }
            );
          } else {
            dropdownElem.hide().empty();
          }
        });
      }

      // 监听输入事件
      var timer = null;
      inputElem.on('input', function () {
        var value = $(this).val().trim();

        // 清除之前的定时器
        clearTimeout(timer);

        // 设置延时，避免频繁请求
        timer = setTimeout(function () {
          searchBrand(value);
        }, 300);
      });

      // 监听获得焦点事件
      inputElem.on('focus', function () {
        var value = $(this).val().trim();
        searchBrand(value);
      });

      // 点击其他区域隐藏下拉框
      $(document).on('click', function (e) {
        if (!$(e.target).closest(inputElem).length && !$(e.target).closest(dropdownElem).length) {
          dropdownElem.hide();
        }
      });
    },
    handleBind: function () {

      //修改客户询价提交
      layui.form.on('submit(updateVerifyInquiryInfoSubmit)', function (data) {
        //处理一下交期
        var delivery_time_val = data.field.delivery_time_val;
        var delivery_time_unit = data.field.delivery_time_unit;
        if (delivery_time_val && delivery_time_unit) {
          data.field.delivery_time = delivery_time_val + delivery_time_unit;
        }
        Request('/api/webInquiry/updateVerifyInquiryInfo', 'POST', data.field, function (res) {
          admin.btnLoading($(data.elem), '');
          if (res.code == 0) {
            layer.msg('操作成功', {shift: 0, time: 2000}, function () {
              layer.closeAll();
              table.reload('list');
            });
          } else {
            admin.btnLoading($(data.elem), false);
            layer.msg(res.msg);
          }
        });
      });

      //生成询价单
      layui.form.on('submit(generateInquirySubmit)', function (data) {
        //处理一下交期
        var delivery_time_val = data.field.delivery_time_val;
        var delivery_time_unit = data.field.delivery_time_unit;
        if (delivery_time_val && delivery_time_unit) {
          data.field.delivery_time = delivery_time_val + delivery_time_unit;
        }
        //先保存询价接口，然后调用生成询价单接口
        Request('/api/webInquiry/updateVerifyInquiryInfo', 'POST', data.field, function (res) {
          admin.btnLoading($(data.elem), '');
          if (res.code == 0) {
            Request('/api/webInquiry/generateInquiry', 'POST', {web_inquiry_item_ids: data.field.web_inquiry_item_id}, function (res) {
              if (res.code == 0) {
                layer.msg(res.msg, {shift: 0, time: 2000}, function () {
                  layer.closeAll();
                  table.reload('list');
                });
              } else {
                layer.msg(res.msg);
              }
            });
          } else {
            admin.btnLoading($(data.elem), false);
            layer.msg(res.msg);
          }
        });
      });

      //网站询价确认提交
      layui.form.on('submit(confirmItemsSubmit)', function (data) {
        var multi_confirm_list = [];
        var is_push_customer = $(this).data('is-push-customer');

        // 先检查是否有选中的数据
        var hasCheckedItems = false;
        $('.data-list .layui-table tbody tr:nth-child(3n)').each(function () {
          var row = $(this);
          var checkbox = row.closest('tr').prev().prev().find('input[type="checkbox"]');
          if (checkbox.prop('checked')) {
            hasCheckedItems = true;
            return false; // 找到一个选中项就跳出
          }
        });

        // 如果没有选中任何项，提示用户
        if (!hasCheckedItems) {
          layer.msg('请至少勾选一条数据');
          return false;
        }

        // 收集表单数据并验证
        var validationPassed = true;
        $('.data-list .layui-table tbody tr:nth-child(3n)').each(function () {
          var row = $(this);

          // 检查对应的checkbox是否被选中
          var checkbox = row.closest('tr').prev().prev().find('input[type="checkbox"]');
          if (!checkbox.prop('checked')) {
            return; // 如果没选中，跳过此行
          }

          // 获取含税单价并验证
          var goods_price_input = row.find('.goods_price');
          var goods_price = parseFloat(goods_price_input.val()) || 0;
          if (goods_price <= 0) {
            // 使用tips提示并定位到出错的输入框
            layer.tips('含税单价必须大于0', goods_price_input, {
              tips: [1, '#FF5722'],
              time: 3000
            });
            goods_price_input.focus();
            validationPassed = false;
            return false; // 跳出循环
          }

          // 获取交期值和单位
          var delivery_time_val = row.find('.delivery_time_val').val();
          var delivery_time_unit = row.find('.delivery_time_unit').val();
          var delivery_time = delivery_time_val + delivery_time_unit;

          // 构建数据对象
          var item = {
            web_inquiry_item_id: row.find('.web_inquiry_item_id').val(),
            quote_id: row.find('.quote_id').val(),
            goods_name: row.find('.goods_name').val(),
            brand_name: row.find('.brand_name').val(),
            quote_number: row.find('.quote_number').val(),
            goods_price: goods_price,
            currency: row.find('.currency').val(),
            delivery_time: delivery_time,
            batch: row.find('.batch').val(),
            expire_time: row.find('.expire_time').val(),
            remark: row.find('.remark').val()
          };

          multi_confirm_list.push(item);
        });

        // 如果验证失败，不调用接口
        if (!validationPassed) {
          return false;
        }

        Request('/api/webInquiry/confirmItems', 'POST', {multi_confirm_list: JSON.stringify(multi_confirm_list), is_push_customer: is_push_customer}, function (res) {
          admin.btnLoading($(data.elem), '');
          if (res.code == 0) {
            layer.msg('操作成功', {shift: 0, time: 2000}, function () {
              layer.closeAll();
              table.reload('list');
            });
          } else {
            admin.btnLoading($(data.elem), false);
            layer.msg(res.msg);
          }
        });
      });

      return this;
    }
  }

  IndexController.init();

});
