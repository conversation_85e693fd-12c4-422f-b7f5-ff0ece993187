layui.use(['admin', 'index', 'form', 'table', 'laydate'], function () {
    var admin = layui.admin;
    var index = layui.index;
    var form = layui.form;
    var table = layui.table;
    var laydate = layui.laydate;

    window.IndexController = {
        init: function () {
            this.created(this).render(this).handleBind(this);
        },
        created: function () {

            //时间初始化
            laydate.render({
                elem: '#time',
                type: 'date',
                range: "~"
            });

            //客户名称初始化组件
            xmSelect.render({
                el: '#create_name',
                tips: '请选择销售员',
                radio: true,
                searchTips: '请输入',
                filterable: true,
                remoteSearch: true,
                clickClose: true,
                name: 'create_name',
                remoteMethod: function (val, cb, show) {
                    if (!val) {
                        return cb([]);
                    }
                    Request('/api/inquiry/getSeller', 'GET', {keyword: val}, function (res) {
                        if (res.code == 0) {
                            var data = [];
                            if (res.data.list.length > 0) {
                                res.data.list.forEach(item => {
                                    data.push({
                                        name: item,
                                        value: item
                                    });
                                });
                            }
                            cb(data);
                        }
                    });
                }
            })


            return this;
        },
        render: function () {

            //列表渲染
            table.render({
                elem: '#list',
                url: '/api/webInquiry/getWebInquiryList',
                toolbar: '#toolbar',
                method: 'POST',
                page: true,
                size: 'sm',
                cellMinWidth: 80,
                defaultToolbar: ['filter'],
                limit: 15,
                limits: [15, 50, 100, 200],
                cols: [[
                    {type: 'checkbox', width: 50, align: 'center', fixed: 'left'},
                    {type: 'numbers', title: '序号', width: 50, align: 'center', fixed: 'left'},
                    {
                        field: 'web_inquiry_sn', title: '网站询价单号', width: 150, align: 'left', templet(d) {
                            return `<a class="alink" ew-href="/webInquiry/webInquiryDetail?web_inquiry_id=${d.web_inquiry_id}" ew-title="${d.web_inquiry_sn}-网站询价详情">${d.web_inquiry_sn}</a>`
                        }
                    },
                    {field: 'customer_name', title: '询价客户', minWidth: 130, align: 'left'},
                    {field: 'customer_account', title: '账号', width: 130, align: 'left'},
                    {field: 'total_item_count', title: '总型号数', width: 80, align: 'left'},
                    {field: 'unconfirmed_count', title: '未确认报价数', width: 113, align: 'left'},
                    {field: 'confirmed_count', title: '已确认报价数', width: 113, align: 'left',},
                    {field: 'ordered_count', title: '已下单型号数', width: 113, align: 'left'},
                    {field: 'delivery_place_val', title: '交货地', width: 100, align: 'left'},
                    {field: 'create_name', title: '销售员', width: 100, align: 'left'},
                    {field: 'create_time', title: '创建时间', width: 150, align: 'left'},
                    {field: 'update_time', title: '更新时间', width: 150, align: 'left'}
                ]],
                parseData: LayUiTableParseData,
                done: function (res, curr, count) {
                    layui.form.render();
                }
            });

            //查询搜索
            form.on('submit(getList)', function (data) {
                table.reload('list', {
                    where: data.field,
                    page: {
                        curr: 1
                    }
                });
            });

            //监听头工具栏事件
            table.on('toolbar(list)', function (obj) {

                var checkStatus = table.checkStatus(obj.config.id);
                var data = checkStatus.data;

                switch (obj.event) {
                    //领取客户询价
                    case 'takeWebInquiry':
                        if (data.length == 0) {
                            layer.msg('请至少勾选一条数据！');
                            return;
                        }
                        var hasAssignedSales = data.some(item => item.sale_name && item.sale_name.trim() !== '');
                        if (hasAssignedSales) {
                            layer.msg('只能领取未分配销售员的询价单');
                            return;
                        }
                        layer.confirm(`领取后，该客户将同步到CRM客户管理系统更新绑定关系`, {
                            skin: 'layui-layer-admin',
                            title: '领取客户询价',
                            resize: false,
                            offset: '250px',
                            area: ['400px', 'auto'],
                            move: false
                        }, function (i) {
                            var web_inquiry_id = data.map(item => item.web_inquiry_id);
                            Request('/api/webInquiry/takeWebInquiry', 'POST', {web_inquiry_id: web_inquiry_id.join(',')}, function (res) {
                                if (res.code == 0) {
                                    layer.msg(res.msg, {shift: 0, time: 2000}, function () {
                                        layer.closeAll();
                                        table.reload('list');
                                    });
                                } else {
                                    layer.msg(res.msg);
                                }
                            });
                        });
                        break;
                    //导出
                    case 'export':
                        break;
                }
            });

            //触发单元格工具事件
            table.on('tool(list)', function (obj) {
                var data = obj.data;
                switch (obj.event) {

                }
            });

            return this;
        },
        handleBind: function () {


            return this;
        }
    }

    IndexController.init();

});
