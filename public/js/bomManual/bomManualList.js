layui.use(['form', 'table', 'laydate', 'xmSelect', 'layer'], function () {
  var form = layui.form;
  var table = layui.table;
  var laydate = layui.laydate;
  var xmSelect = layui.xmSelect;
  var layer = layui.layer;
  var index = layui.index;
  layui.form.render();

  //日期范围
  laydate.render({
    elem: '#search_date',
    range: "~",
    trigger: 'click',
    value: '',
    type: 'date',
    extrabtns: [
      {
        id: 'lastday-0',
        text: '今天',
        range: [new Date(new Date().setDate(new Date().getDate())), new Date(new Date().setDate(new Date().getDate() + 1))]
      },
      {
        id: 'lastday-3',
        text: '近三天',
        range: [new Date(new Date().setDate(new Date().getDate() - 3)), new Date(new Date().setDate(new Date().getDate()))]
      },
      {
        id: 'lastday-7',
        text: '本周',
        range: [new Date(new Date().setDate(new Date().getDate() - 7)), new Date(new Date().setDate(new Date().getDate()))]
      },
      {
        id: 'lastday-30',
        text: '本月',
        range: [yy, new Date(new Date().setDate(new Date().getDate()))]
      },
      {
        id: 'lastday-182',
        text: '半年',
        range: [dt, new Date(new Date().setDate(new Date().getDate()))]
      }
    ],
    done: function (val, stdate, ovdate) {

    }
  });

  var renderTable = function () {
    table.render({
      id: 'list',
      elem: '#list',
      url: '/api/bomManual/getBomManualList',
      method: 'post',
      cellMinWidth: 80,
      page: true,
      size: "sm",
      where: {
        type: 1,
      },
      cols: [
        [
          {title: '序号', type: 'numbers'},
          {
            field: '', title: '操作', width: 180, toolbar: "#toorBar"
          },
          {
            field: 'bom_sn', title: 'BOM编号', width: 180, templet: function (d) {
              return '<a class="alink" ew-title="' + d.bom_sn + '" ew-href="/bomManual/detail?id=' + d.id + '">' + d.bom_sn + '</a>'
            }
          },
          {field: 'bom_title', title: 'BOM名称', width: 200},
          {field: 'assigned_user_name', title: '指定采购员', width: 120},
          {
            field: 'status_name', title: '状态', width: 100, templet: function (d) {
              var statusClass = '';
              switch (d.status) {
                case 1:
                  statusClass = 'layui-bg-orange';
                  break;
                case 2:
                  statusClass = 'layui-bg-green';
                  break;
                case 3:
                  statusClass = 'layui-bg-blue';
                  break;
              }
              return '<span class="layui-badge ' + statusClass + '">' + d.status_name + '</span>';
            }
          },
          {field: 'create_name', title: '创建人', width: 100},
          {field: 'create_time', width: 150, title: '创建时间'},
          {field: 'update_time', width: 150, title: '更新时间'},
          {field: 'bom_remark', title: '备注', minWidth: 200}
        ]],
      limit: 10,
      limits: [10, 20, 50],
      parseData: LayUiTableParseData,
      done: function (data) {
        $(".tablebtnsx").hover(function () {
          var t_ = $(this).offset().top;
          var l_ = $(this).offset().left;
          var guid = $(this).attr("guid");
          $(".btncen .pdfbtng").attr("href", "/bomManual/bomManualPdf?id=" + guid)
          $(".btncen .excelbtng").attr("href", "/bomManual/bomManualExcel?id=" + guid)
          $(".btncen").css({
            left: l_,
            top: t_ + 20
          })
        }, function (e) {
        })
        $(".btncen").hover(function (e) {
        }, function () {
          $(".btncen").css({
            left: 9999,
            top: 9999
          })
        })
      }
    });
  };
  renderTable();

  // 监听工具条
  table.on('tool(list)', function(obj){
    var data = obj.data;
    if(obj.event === 'detail'){
      // 查看详情
      layer.open({
        type: 2,
        title: 'BOM详情',
        shadeClose: true,
        shade: 0.8,
        area: ['80%', '80%'],
        content: '/bomManual/detail?id=' + data.id
      });
    } else if(obj.event === 'edit'){
      // 编辑
      layer.open({
        type: 2,
        title: '编辑BOM',
        shadeClose: true,
        shade: 0.8,
        area: ['60%', '70%'],
        content: '/bomManual/edit?id=' + data.id,
        end: function(){
          // 刷新表格
          table.reload('list');
        }
      });
    } else if(obj.event === 'delete'){
      // 删除
      layer.confirm('确定删除这条BOM记录吗？', function(index){
        $.ajax({
          url: '/api/bomManual/deleteBomManual',
          type: 'post',
          data: {
            id: data.id
          },
          dataType: 'json',
          success: function (res) {
            if (res.code == 200) {
              layer.msg('删除成功', {icon: 1});
              table.reload('list');
            } else {
              layer.msg(res.msg || '删除失败', {icon: 2});
            }
          },
          error: function () {
            layer.msg('网络错误，请重试', {icon: 5});
          }
        });
        layer.close(index);
      });
    }
  });

  $("body").click(function (e) {
    $(".btncen").css({
      left: 9999,
      top: 9999
    })
  })

  //创建人搜索
  var skuSelect = xmSelect.render({
    el: '#user',
    autoRow: false,
    radio: true,
    toolbar: {show: false},
    remoteSearch: true,
    filterable: true,
    clickClose: true,
    data: [],

    remoteMethod: function (val, cb, show) {
      //这里如果val为空, 则不触发搜索
      if (!val) return cb([]);

      $.ajax({
        url: '/api/search/getQuotedUser',
        type: 'post',
        data: {
          keyword: val,
        },
        dataType: 'json',
        timeout: 1000,
        success: function (res) {
          var a = [];
          var arr_ = res.data.list;
          for (var i = 0; i < arr_.length; i++) {
            a.push({
              name: arr_[i],
              value: arr_[i]
            })
          }
          cb(a)
        },
        error: function () {
          return layer.msg('网络错误，请重试', {icon: 5});
        }
      });
    }
    , on: function (data) {
      var arr = data.arr
      if (arr.length == 0) {
        console.log("数组为空");
        $("#create_name").val("");
      } else {
        var json = [];
        var j = {};
        j.value = data.arr[0].value;
        j.name = data.arr[0].name;
        json.push(j);
        var brand = JSON.stringify(json);
        var brand = json;
        $("#create_name").val(brand[0].value);
      }
    }
  })

  form.on('submit(load)', function (data) {
    //执行重载
    var data_ = data.field

    table.reload('list', {
      where: data_,
      page: {
        curr: 1
      }
    });

    return false;
  });

});
