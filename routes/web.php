<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

use Illuminate\Http\Request;

//首页
Route::get('/', "IndexController@index");    // 首页
Route::get('/index/dashboard', "IndexController@dashboard");    // 仪表盘
Route::get('/index/actuals', "IndexController@actuals");    // 找现货
Route::get('/index/agentBuy', "IndexController@agentBuy");  // 找代购
Route::get('/index/agentBuyBatch', "IndexController@agentBuyBatch");  // 找代购-批量匹配
Route::get('/index/futures', "IndexController@futures");    // 找期货
Route::get('/index/agency', "IndexController@agency");      // 找代理
Route::get('/index/bom', "IndexController@bom");            // BOM
Route::get('/index/priceAssistant', "IndexController@priceAssistant");   // 查费用

//BOM管理
Route::get('/bom/bomBoard','BomController@bomBoard');   //BOM列表
Route::get('/bom/createBom','BomController@createBom');   //新增BOM
Route::get('/bom/bomDetail','BomController@bomDetail');   //BOM详情
Route::get('/bom/changeBomDetail','BomController@changeBomDetail');   //修改BOM详情
Route::get('/bom/exportBomDetail','BomController@exportBomDetail');   //导出BOM详情


//询价管理
Route::get('/inquiry/inquiryBoard','InquiryController@inquiryBoard');   //询价列表
Route::get('/inquiry/multiAddView', "InquiryController@multiAddView");    //批量新增
Route::get('/inquiry/createOrder', "InquiryController@createOrder");    //生成报价单
Route::get('/inquiry/importView', "InquiryController@importView");    //模板上传

//网站询价管理
Route::get('/webInquiry/webInquiryBoard','WebInquiryController@webInquiryBoard');   //网站询价列表
Route::get('/webInquiry/webInquiryDetail','WebInquiryController@webInquiryDetail');   //网站询价详情

//询价池
Route::get('/inquirypool/inquirePool', "QuoteController@inquirePool");    // 询价池
Route::get('/inquirypool/inquiryQuoteLogList', "InquiryDataController@inquiryQuoteLogList");    // 询报价日志
Route::get('/inquirypool/feedbackList', "InquiryDataController@feedbackList");    // 反馈列表
// 现货列表
Route::get('/sku/skuBoard', "SkuController@skuBoard");    // 现货列表

//报价列表
Route::get('/quote/quoteList', "QuoteController@quoteList");        // 报价列表
Route::get('/quote/supplierQuoteList', "QuoteController@supplierQuoteList");        // 供应商报价列表
Route::get('/quote/confirmQuoteList', "QuoteController@confirmQuoteList");        // 渠道确认列表

// 批量报价
Route::get('/batchquote/exportInquiryPoolExcel', "InquiryController@exportInquiryPoolExcel");  //询价池-导出批量报价Excel
Route::get('/batchquote/exportQuoteListExcel', "QuoteController@exportQuoteListExcel");  //报价列表-导出批量报价Excel
Route::get('/batchquote/exportQuoteAll', "QuoteController@exportQuoteAll");  //数据导出

// 货品关注
Route::get('/collectgoods/goodsFollowList', "CollectGoodsController@goodsFollowList");        // 货品关注列表

//优势货源
Route::get('/best/goodslist', "BestController@goodsList");        // 优势货源列表
Route::get('/best/multiAddView', "BestController@multiAddView");    // 批量添加

//员工信息
Route::get('/user/userList', "UserController@userList");        // 用户列表
Route::get('/user/saleUserList', "UserController@saleUserList");        // 销售用户列表
Route::get('/user/buyUserList', "UserController@buyUserList");        // 采购用户列表
Route::get('/user/detail', "UserController@detail");        // 用户信息
Route::get('/user/saleDetail', "UserController@saleDetail");        // 销售用户详情
Route::get('/user/buyDetail', "UserController@buyDetail");        // 采购用户信息

//pm管理
Route::get('/pm/pmList', "PmController@pmList");        // pm列表

//销售询价统计
Route::get('/saledata/inquiryDataList','InquiryDataController@inquiryDataBoard');       // 询价报表展示
Route::get('/saledata/inquiryDataDetail','InquiryDataController@inquiryDataDetail');    //询价报表详情

// 报价单列表
Route::get('/order/orderList', "OrderController@orderList");    // 生成报价单
Route::get('/order/detail', "OrderController@detail");    // 报价单详情
Route::get('/order/orderPdf', "OrderController@orderPdf");    // 报价单详情
Route::get('/order/orderExcel', "OrderController@orderExcel");    // 报价单出excel

// BOM人工处理
Route::get('/bomManual/bomManualList', "BomManualController@bomManualList");    // BOM人工处理列表
Route::get('/bomManual/detail', "BomManualController@detail");    // BOM详情
Route::get('/bomManual/edit', "BomManualController@edit");    // BOM编辑
Route::get('/bomManual/bomManualPdf', "BomManualController@bomManualPdf");    // BOM导出PDF
Route::get('/bomManual/bomManualExcel', "BomManualController@bomManualExcel");    // BOM导出Excel

//采购报价统计
Route::get('/purdata/quoteDataList','QuoteDataController@quoteDataBoard');         // 报价报表展示
Route::get('/purdata/quoteDataDetail','QuoteDataController@quoteDataDetail');      //报价报表详情

//询价池统计
Route::get('/inquirypooldata/inquiryPoolReport','InquiryDataController@inquiryPoolReport');    //询价池统计报表
Route::get('/inquirypooldata/searchInquiryStatistics','InquiryDataController@searchInquiryStatistics');    //搜索询价统计


//快捷查询助手
Route::get('/quicksearch/searchView','ChannelSyncController@searchView'); //渠道商品同步
Route::get('/quicksearch/mouser','SearchController@mouser');//Mouser查询
Route::get('/quicksearch/tme','SearchController@tme');//TME价格查询
Route::get('/quicksearch/rochester','SearchController@rochester');//rochester查询
Route::get('/quicksearch/aggrGoods','SearchController@aggrGoods');//货品查询聚合页
Route::get('/quicksearch/searchLogList','SearchLogsController@searchLogList');//搜索日志

// 分配规则
Route::get('/allocation/ruleView','AllocationController@ruleView');      // 分配规则页面
Route::get('/notice/list','NoticeController@noticeList');      // 分配规则页面

// 供应商报价统计
Route::get('/quotedata/getSupplierQuoteDataBoard', "SupplierQuoteDataController@getSupplierQuoteDataBoard");    // 供应商报价统计
Route::get('/quotedata/exportSupplierQuoteData', "SupplierQuoteDataController@exportSupplierQuoteData");        // 导出供应商报价统计数据
Route::get('/quotedata/exportSupplierQuoteDetail', "SupplierQuoteDataController@exportSupplierQuoteDetail");    // 导出供应商报价统计明细

// 运营查询数据
Route::get('/franchise/todayData', 'FranchiseController@todayData');

// 自动报价统计
Route::get('/autoQuote/autoQuoteBoard', 'QuoteDataController@autoQuoteBoard'); // 自动报价统计

// 商品关税管理
Route::get('/tax/goodsTaxList', 'TaxController@goodsTaxList'); // 商品关税查询

