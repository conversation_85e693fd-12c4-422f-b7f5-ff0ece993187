<?php

use Illuminate\Support\Facades\Route;


// 首页
Route::post('/index/getSearchMetaInfo', "IndexController@getSearchMetaInfo");    // 首页搜索-基础参数
Route::post('/index/getSaleItemNums', "IndexController@getSaleItemNums");    // 销售事项数据
Route::post('/index/getPurItemNums', "IndexController@getPurItemNums");    // 采购事项数据
Route::post('/index/getSaleChartNums', "IndexController@getSaleChartNums");    // 销售图表数据
Route::post('/index/getPurChartNums', "IndexController@getPurChartNums");    // 采购图表数据
Route::post('/index/getInquiryGoodsRanking', "IndexController@getInquiryGoodsRanking");    // 获取询价排名

// 消息设置
Route::get('/msg/getSysMsgConfig', 'UserSysMsgController@getSysMsgConfig');//查询关注消息分类
Route::post('/msg/setSysMsgConfig', 'UserSysMsgController@setSysMsgConfig');//设置关注消息分类

//联想相关接口，关键字联想
Route::post('/search/getAgentLinkUrl', "SearchController@getAgentLinkUrl");    //查询代购数据跳转链接
Route::post('/search/getQuotedUser', "SearchController@getQuotedUser");    //查询参与报价用户
Route::post('/search/getInquiryUser', "SearchController@getInquiryUser");    //查询参与询价用户
Route::post('/search/getDepartmentList', 'InquiryDataController@getDepartmentList');//搜索部门接口
Route::post('/search/getSellerList', 'InquiryDataController@getInquiryUser');//销售人员搜索接口
Route::post('/search/getQuoteUserList', 'QuoteDataController@getQuoteUserList');//采购人员搜索接口
Route::get('/search/getBrand', 'InquiryController@brandSearch');//查询标准品牌
Route::get('/search/getSupplier', 'SearchController@getSupplier');//查询供应商
Route::post('/search/getCustomer', 'SearchController@getCustomer');//查询客户
Route::post('/search/getBuyer', 'SearchController@getBuyer');//查询采购员
Route::post('/search/getBuyerList', 'SearchController@getBuyerList');//查询采购员
Route::post('/search/getBuyerDepartRecursionList', 'DepartmentController@getBuyerDepartRecursionList');//查询采购分级部门
Route::post('/search/getSaleDepartRecursionList', 'DepartmentController@getSaleDepartRecursionList');//查询销售分级部门
Route::post('/search/getAllDepartRecursionList', 'DepartmentController@getAllDepartRecursionList');//查询分级部门
Route::post('/search/getUsersByGoodsName', 'SearchController@getUsersByGoodsName'); //型号品牌搜索同事
Route::post('/search/getTaxEccnInfo', 'SearchController@getTaxEccnInfo'); //查询关税ECCN信息

// 货品查询相关接口
Route::post('/search/indexRankingData', 'SearchController@indexRankingData');//首页数据排名
Route::post('/search/searchWorkmate', 'SearchController@searchWorkmate');//首页查询同事
Route::post('/search/getSearchLog', 'SearchController@getSearchLog');//首页获取历史搜索记录
Route::post('/search/getHistory', 'SearchController@getHistory');//历史报价
Route::post('/search/getPurHistory', 'SearchController@getPurHistory');//历史成交
Route::post('/search/getBestGoods', 'SearchController@getBestGoods');//优势货源
Route::post('/search/getMonopolyGoods', 'SearchController@getMonopolyGoods');//猎芯专营
Route::post('/search/getAgentGoods', 'SearchController@getAgentGoods');//代购货源
Route::post('/search/getSelfGoods', 'SearchController@getSelfGoods');//自营货源
Route::post('/search/getAggrCount', 'SearchController@getAggrCount');//搜索数量统计
Route::post('/search/getMyHistoryPurOrder', 'SearchController@getMyHistoryPurOrder');//我的历史采购记录
Route::post('/search/getPurItems', 'SearchController@getPurItems');//首页搜索采购单
Route::post('/search/getOrderItems', 'SearchController@getOrderItems');//首页搜索订单
Route::post('/search/getAvailableInquiryList', 'SearchController@getAvailableInquiryList');//首页搜索当前可报价询价单
Route::get('/search/getActualsByGoodsName', 'SearchController@getActualsByGoodsName'); //搜索现货-型号
Route::post('/search/getActualsByBrandName', 'SearchController@getActualsByBrandName'); //搜索现货-品牌
Route::get('/search/getIndexAgentGoods', 'SearchController@getIndexAgentGoods');       //首页-找代购
Route::get('/search/getBatchIndexAgentGoods', 'SearchController@getBatchIndexAgentGoods');       //首页-找代购-批量匹配
Route::get('/search/getFuturesByGoodsName', 'SearchController@getFuturesByGoodsName'); //搜索期货-型号
Route::get('/search/getFuturesByBrandName', 'SearchController@getFuturesByBrandName'); //搜索期货-品牌
Route::get('/search/getGoodAtFuturesBuyer', 'SearchController@getGoodAtFuturesBuyer'); //搜索擅长期货采购

// 搜索日志
Route::get('/search/getSearchLogList', 'SearchLogsController@getSearchLogList');//获取搜索记录

//智能解析相关接口
Route::post('/analysis/getMeanInfo', "AnalysisController@getMeanInfo");    // 解析文本

//BOM管理
Route::post('/bom/addBom', 'BomController@addBom');   //新增BOM
Route::post('/bom/cancelBom', 'BomController@cancelBom');   //取消BOM
Route::post('/bom/urgeBuyerCheck', 'BomController@urgeBuyerCheck');   //催采购核价
Route::post('/bom/updateBomItemsGoodsId', 'BomController@updateBomItemsGoodsId');//更新bom详情对应的商品
Route::post('/bom/batchBomItems', 'BomController@batchBomItems');   //重新匹配多个bom详情
Route::post('/bom/updateSelectBomMatch', 'BomController@updateSelectBomMatch');   //选择是否智能询价
Route::post('/bom/batchReInquiry', 'BomController@batchReInquiry');   //批量重新生成询报价
Route::get('/bom/exportBomDetail', 'BomController@exportBomDetail');   //导出BOM详情

//商品关税管理
Route::get('/tax/getGoodsTaxList', 'TaxController@getGoodsTaxList');   //商品关税查询
Route::post('/tax/addConsultTask', 'TaxController@addConsultTask');   //新建咨询任务
Route::post('/tax/getGoodsTaxListByExcel', 'TaxController@getGoodsTaxListByExcel');   //商品关税查询-excel上传筛选
Route::post('/tax/addItemsRemark', 'TaxController@addItemsRemark');   //添加备注
Route::post('/tax/addItemsControl', 'TaxController@addItemsControl');   //标记管制
Route::post('/tax/cancelItemsControl', 'TaxController@cancelItemsControl');   //取消管制
Route::post('/tax/syncItemsErp', 'TaxController@syncItemsErp');   //推送erp

//询价管理
Route::get('/inquiry/getInquiryDefaultSetting', "UserController@getInquiryDefaultSetting");    // 获取询价默认值设置
Route::post('/inquiry/updateInquiryDefaultSetting', "UserController@updateInquiryDefaultSetting");    // 更新询价默认值设置
Route::post('/inquiry/getInquiryList', "InquiryController@getInquiryList");//获取询价单
Route::post('/inquiry/getQuoteListByInquiryItemId', "QuoteController@getQuoteListByInquiryItemId");    //询价单报价列表
Route::get('/inquiry/getFranchiseQuoteList', "QuoteController@getFranchiseQuoteList");    //获取专营现货
Route::post('/inquiry/getBuyAgentQuoteList', "QuoteController@getBuyAgentQuoteList");      //获取代购现货
Route::post('/inquiry/selectQuote', "QuoteController@selectQuote");    //选择报价
Route::post('/inquiry/addInquiry', "InquiryController@addInquiry");   //单条新增询价
Route::post('/inquiry/forceAssign', "InquiryController@forceAssign");   //强制分配
Route::post('/inquiry/freeToPool', "InquiryController@freeToPool");   //询价单释放公海池
Route::post('/inquiry/updateInquiry', "InquiryController@updateInquiry");   //修改询价
Route::get('/inquiry/getSeller', "InquiryController@sellerSearch");//查询采购员
Route::post('/inquiry/multiAdd', 'InquiryController@multiAdd');//批量新增询价
Route::post('/inquiry/multiAddFutures', 'InquiryController@multiAddFutures');//期货模板上传
Route::post('/inquiry/showImportDataResult', "InquiryController@showImportDataResult"); //模板上传获取excel数据结果
Route::post('/inquiry/showImportFuturesDataResult',
    "InquiryController@showImportFuturesDataResult"); //期货模板上传获取excel数据结果
Route::post('/inquiry/showSmartImportDataResult', "InquiryController@showSmartImportDataResult"); //其他模板上传获取excel数据结果
Route::post('/inquiry/showOrderImportDataResult', "InquiryController@showOrderImportDataResult"); //销售系统-发起询价单
Route::post('/inquiry/closeInquiry', "InquiryController@closeInquiry"); //关闭询价单接口
Route::post('/inquiry/stopQuote', "InquiryController@stopQuote"); //停止报价接口
Route::post('/inquiry/mgetinfoByInquiryItemIds', "QuoteController@mgetinfoByInquiryItemIds");    //根据报价ids，获取报价信息
Route::post('/inquiry/resetAgentQuote', "QuoteController@autoQuoteByBuyAgent"); //更新代购数据
Route::match(["post", "get"], '/inquiry/exportPreExcel', "InquiryController@exportPreExcel"); //excel导出
Route::post('/inquiry/getOrderItemList', "InquiryController@getOrderItemList"); //我的历史销售
Route::post('/inquiry/getQuoteTrackInfo', "InquiryController@getQuoteTrackInfo"); //当前报价跟踪
Route::post('/inquiry/reAssignBuyer', "InquiryController@reAssignBuyer"); //重新分配采购员
Route::post('/inquiry/getGoodsHistoryQuoteList', "InquiryController@getGoodsHistoryQuoteList");    // 获取商品型号的历史报价
Route::post('/inquiry/urgeBuyerQuote', "InquiryController@urgeBuyerQuote");    // 催促采购报价
Route::post('/inquiry/getQuotedUpdateLogList', "QuoteController@getQuotedUpdateLogList");    // 报价修改记录
Route::post('/inquiry/getQuoteDetailUpdateLogs', "InquiryController@getQuoteDetailUpdateLogs");    // 销售查看报价修改记录及详情
Route::post('/inquiry/addBuyerQuote', "InquiryController@addBuyerQuote");    // 销售增加采购报价
Route::post('/inquiry/pointBuyerQuote', "InquiryController@pointBuyerQuote");    // 销售指定采购报价
Route::post('/inquiry/addRelaBom', "InquiryController@addRelaBom");    // 询价单关联bom
Route::post('/inquiry/delNewTag', "InquiryController@delNewTag");        // 询价去掉新标记

// 网站询价
Route::post('/webInquiry/getWebInquiryList', 'WebInquiryController@getWebInquiryList');   //获取网站询价列表
Route::post('/webInquiry/takeWebInquiry', 'WebInquiryController@takeWebInquiry');   //领取询价
Route::post('/webInquiry/getWebInquiryDetail', 'WebInquiryController@getWebInquiryDetail');   //网站询价详情
Route::post('/webInquiry/getWebInquiryItemList', 'WebInquiryController@getWebInquiryItemList');   //获取网站询价明细列表
Route::post('/webInquiry/updateVerifyInquiryInfo', 'WebInquiryController@updateVerifyInquiryInfo');   //修改客户询价
Route::post('/webInquiry/generateInquiry', 'WebInquiryController@generateInquiry');   //生成询价单
Route::post('/webInquiry/getConfirmItemList', 'WebInquiryController@getConfirmItemList');   //获取网站询价明细确认信息
Route::post('/webInquiry/confirmItems', 'WebInquiryController@confirmItems');   //网站询价确认


//报价单列表
Route::post('/order/getOrderList', "OrderController@getOrderList");//获取报价单列表
Route::post('/order/info', "OrderController@getInfo");    // 获取报价单详情
Route::post('/order/addOrder', "OrderController@addOrder");    //生成报价单

//询价池
Route::post('/inquirypool/getInquiryPoolList', "InquiryController@getInquiryPoolList"); //询价列表接口
Route::post('/inquirypool/takeInquiry', "InquiryController@takeInquiry");  //领取询价

// 批量报价
Route::post('/batchquote/importInquiryPoolExcel', "QuoteController@importInquiryPoolExcel");  //询价池-导入报价模板Excel
Route::post('/batchquote/importQuoteListExcel', "QuoteController@importQuoteListExcel");  //报价列表-导入报价模板Excel

//报价列表
Route::get('/quote/getQuoteDefaultSetting', "UserController@getQuoteDefaultSetting");    // 获取报价默认值设置
Route::post('/quote/updateQuoteDefaultSetting', "UserController@updateQuoteDefaultSetting");    // 更新报价默认值设置
Route::post('/quote/getUserQuoteList', "QuoteController@getUserQuoteList");    // 获取用户报价列表
Route::post('/quote/addQuote', "QuoteController@addQuote");          // 新增报价
Route::post('/quote/copyQuote', "QuoteController@copyQuote");          // 复制新增报价
Route::post('/quote/updateQuote', "QuoteController@updateQuote");          // 修改报价
Route::get('/quote/closeQuote', "QuoteController@closeQuote");      // 关闭报价
Route::get('/quote/closeSupplyQuote', "QuoteController@closeSupplyQuote");      // 关闭供应商报价
Route::post('/quote/getHistoryList', "QuoteController@getHistoryList");    // 获取历史报价
Route::post('/quote/getOtherQuoteList', "QuoteController@getOtherQuoteList");    // 获取其他采购报价
Route::get('/quote/getInquiryItem', "QuoteController@getInquiryItem");    // 获取报价对应的询价明细
Route::post('/quote/seekQuote', "QuoteController@seekQuote");    // 正在找货
Route::post('/quote/transferQuote', "QuoteController@transferQuote");    // 转让报价单
Route::post('/quote/getSupplierQuoteList', "QuoteController@getSupplierQuoteList");        // 获取供应商报价列表
Route::post('/quote/updateSupplierQuote', "QuoteController@updateSupplierQuote");        // 修改确认供应商报价
Route::post('/quote/markOrdered', "QuoteController@markOrdered");        // 供应商报价标记已下单
Route::post('/quote/getConfirmQuoteList', "QuoteController@getConfirmQuoteList");        // 获取渠道确认列表
Route::post('/quote/handleConfirmQuote', "QuoteController@handleConfirmQuote");        // 处理渠道确认

// 货品关注
Route::get('/collectgoods/getGoodsFollowList', "CollectGoodsController@getGoodsFollowList");    // 货品关注列表
Route::post('/collectgoods/addGoodsFollow', "CollectGoodsController@addGoodsFollow");    // 货品关注单条新增
Route::post('/collectgoods/batchDeleteGoodsFollow', "CollectGoodsController@batchDeleteGoodsFollow");    // 货品关注批量删除
Route::post('/collectgoods/uploadGoodsFollowExcel', "CollectGoodsController@uploadGoodsFollowExcel");    // 货品关注批量新增

//采购现货
Route::post('/best/GetBestGoodsList', "BestController@GetBestGoodsList");    // 查询采购现货列表
Route::post('/best/del', "BestController@del");    // 删除采购现货
Route::post('/best/import', "BestController@import");    // 模板导入
Route::post('/best/add', "BestController@Add");    // 单条添加采购现货
Route::post('/best/multiAdd', "BestController@multiAdd");    // 批量添加

//员工信息
Route::post('/user/getOrderCustomerPhone', "OrderController@getOrderCustomerPhone");    // 获取报价单客户手机号
Route::post('/user/getCustomerPhone', "UserController@getCustomerPhone");    // 获取询价单客户手机号
Route::post('/user/getAccount', "UserController@getAccount");    // 获取同事脱敏数据
Route::post('/user/getUserList', "UserController@getUserList");    // 用户列表
Route::post('/user/getSaleUserList', "UserController@getSaleUserList");    // 销售用户列表
Route::post('/user/getBuyUserList', "UserController@getBuyUserList");    // 采购用户列表
Route::post('/user/updateUserInfo', "UserController@updateUserInfo");     // 修改用户信息
Route::post('/user/updateUsedBrands', "UserController@updateUsedBrands"); // 修改擅长品牌
Route::post('/user/updateUsedCustoms', "UserController@updateUsedCustoms"); // 修改擅长客户
Route::post('/user/followUser', "UserController@followUser"); // 关注用户
Route::post('/user/unFollowUser', "UserController@unFollowUser"); // 取消关注
Route::post('/user/updateUserEmployeeStatus', "UserController@updateUserEmployeeStatus"); // 更新入职状态
Route::post('/user/getRecentInquiryList', "UserController@getRecentInquiryList"); // 获取最近询价数据
Route::post('/user/getRecentOrderGoodsList', "UserController@getRecentOrderGoodsList"); // 获取最近销售型号数据
Route::post('/user/getRecentOrderBrandList', "UserController@getRecentOrderBrandList"); // 获取最近销售品牌数据
Route::post('/user/getRecentBestGoodsList', "UserController@getRecentBestGoodsList"); // 获取最近优势货源品牌数据
Route::post('/user/getRecentQuoteList', "UserController@getRecentQuoteList"); // 获取最近报价数据
Route::post('/user/getRecentPurList', "UserController@getRecentPurList"); // 获取最近采购数据
Route::post('/user/updateListShowRuleBySale', "UserController@updateListShowRuleBySale"); // 更新销售列表展示权限
Route::post('/user/updateListShowRuleByBuy', "UserController@updateListShowRuleByBuy"); // 更新采购列表展示权限
Route::post('/user/getMyFansList', "UserController@getMyFansList"); // 获取员工粉丝列表
Route::post('/user/getBuyerDetailChartData', "UserController@getBuyerDetailChartData"); // 查询采购详情图数据

Route::post('/user/updateUserGoodat', "UserController@updateUserGoodat");     // 修改采购 新品牌 期货 按钮
Route::post('/user/getBuyGoodAtList', "UserGoodsAtController@getBuyGoodAtList");     // 获取采购擅长事项
Route::post('/user/delBuyGoodAtItem', "UserGoodsAtController@delBuyGoodAtItem");     // 删除采购擅长事项
Route::post('/user/updateGoodAtItemById', "UserGoodsAtController@updateGoodAtItemById");     // 修改采购擅长事项
Route::post('/user/addBuyGoodAtItem', "UserGoodsAtController@addBuyGoodAtItem");     // 单条新增采购擅长事项
Route::post('/user/importBuyGoodAtExcel', "UserGoodsAtController@importBuyGoodAtExcel");     // 批量上传采购擅长事项

// pm管理
Route::post('/pm/getPmList', "PmController@getPmList");     // 获取pm列表
Route::post('/pm/searchBrand', "PmController@searchBrand"); // 搜索标准品牌
Route::post('/pm/getClassList', "PmController@getClassList"); //获取一级分类列表
Route::post('/pm/getBuyersForPm', "PmController@getBuyersForPm"); //获取PM下级采购员
Route::post('/pm/addPm', "PmController@addPm");             // 添加pm
Route::post('/pm/updatePm', "PmController@updatePm");             // 修改pm
Route::post('/pm/multiUpdate', "PmController@multiUpdate");       // 批量修改
Route::post('/pm/enablePm', "PmController@enablePm");             // 启用pm
Route::post('/pm/disablePm', "PmController@disablePm");             // 禁用pm
Route::post('/pm/importPmListExcel', "PmController@importPmListExcel");  //模板导入pm


//销售询价统计
Route::match(["post", "get"], '/saledata/inquiryDataList', "InquiryDataController@inquiryDataList");//获取询价报表数据
Route::post('/saledata/getSellerDetail', 'InquiryDataController@getSellerInquiryData');//获取销售询价明细

//采购报价统计
Route::match(["post", "get"], '/purdata/quoteDataList', 'QuoteDataController@quoteDataList');//获取销售报表数据
Route::post('/purdata/getQuoteUserDetail', 'QuoteDataController@getQuoteUserData');//获取采购报价明细

//询价池统计
Route::get('/inquirypooldata/getInquiryPoolReportData', 'InquiryDataController@getInquiryPoolReportData');//获取询价池统计数据

// 自动报价统计
Route::get('/autoQuote/getAutoQuoteData', 'QuoteDataController@getAutoQuoteData'); // 获取自动报价统计数据

//快捷查询助手
Route::get('/quicksearch/search', "ChannelSyncController@search"); //渠道价格同步

// excel
Route::post('/exceltpl/appointColumns', "ExcelTplController@appointColumns");    // excel保存选中excel列指纹

// 分配规则
Route::get('/allocation/getRuleList', 'AllocationController@getRuleList');       // 获取分配规则列表
Route::post('/allocation/addRule', 'AllocationController@addRule');              // 新增分配规则
Route::post('/allocation/editRule', 'AllocationController@editRule');            // 编辑分配规则
Route::post('/allocation/changeRuleStatus', 'AllocationController@changeRuleStatus');            // 停用/启用分配规则

// 通知管理
Route::match(["post", "get"], '/notice/add', 'NoticeController@add');            //新增通知
Route::match(["post", "get"], '/notice/update', 'NoticeController@update');            //更新通知
Route::match(["post", "get"], '/notice/list', 'NoticeController@list');            //通知列表
Route::match(["post", "get"], '/notice/positionList', 'NoticeController@positionList');            //职位列表
Route::match(["post", "get"], '/notice/departmentList', 'NoticeController@departmentList');            //部门列表
Route::match(["post", "get"], '/notice/userList', 'NoticeController@userList');            //用户列表
Route::match(["post", "get"], '/notice/send', 'NoticeController@send');            //发送通知信息
Route::match(["post", "get"], '/notice/get', 'NoticeController@get');            //获取通知详情

// 个人设置
Route::get('/me/getMyEmailSetting', 'UserController@getMyEmailSetting');                    // 邮件管理列表
Route::post('/me/updateMyEmailSetting', 'UserController@updateMyEmailSetting');             // 修改邮件设置
Route::post('/me/updateQuoteConfig', 'UserController@updateQuoteConfig');             // 修改报价设置
Route::post('/me/batchUpdateQuoteLimit', 'UserController@batchUpdateQuoteLimit');             // 批量编辑报价上限
Route::post('/me/infoUpdateQuoteLimit', 'UserController@infoUpdateQuoteLimit');             // 员工详情编辑报价上限
Route::post('/me/batchUpdateRelaPm', 'UserController@batchUpdateRelaPm');             // 批量设置PM
Route::post('/me/getSaleAppClass', 'UserController@getSaleAppClass');             // 销售擅长应用领域列表
Route::post('/me/getAppClassTwo', 'UserController@getAppClassTwo');             // 获取二级擅长应用领域
Route::post('/me/addSaleAppClass', 'UserController@addSaleAppClass');             // 新增销售擅长应用领域
Route::post('/me/delSaleAppClass', 'UserController@delSaleAppClass');             // 删除销售擅长应用领域

// 供应商报价统计
Route::post('/quotedata/getSupplierQuoteData', "SupplierQuoteDataController@getSupplierQuoteData");    // 获取供应商报价统计数据

//报表统计栏目
Route::get('reportStatistics/searchInquiryStatistics', 'ReportStatisticsController@searchInquiryStatistics');//销售询价统计

//Bom接口
Route::get('bom/getBomList', 'BomController@getBomList');//bom列表
Route::get('bom/getBomDetailList', 'BomController@getBomDetailList');//bom详情列表
Route::get('bom/getBomStatusNum', 'BomController@getBomStatusNum');//获取bom各个状态的数量
Route::post('bom/saveBom', 'BomController@saveBom');//保存bom
Route::post('bom/bomRelaInquiry', 'BomController@bomRelaInquiry');//bom关联询价
Route::post('bom/showImportDataResult', 'BomController@showImportDataResult');//获取导入dom模板内容
Route::post('bom/getBomItemListForOrder', 'BomController@getBomItemListForOrder');//bom转报价单-获取明细信息


Route::get('sku/getSkuList', 'SkuController@getSkuList'); // 获取现货列表
Route::get('sku/getSkuListByGoodsIds', 'SkuController@getSkuListByGoodsIds'); // 根据商品id列表获取商品信息

//获取汇率接口
Route::get('rate/getCnyRate', 'ReportStatisticsController@getCnyRate');

Route::get('/inquiryQuoteLog/getInquiryQuoteLogList', 'InquiryDataController@getInquiryQuoteLogList');//获取询报价日志列表

Route::get('/inquiryQuoteLog/getFeedbackList', 'InquiryDataController@getFeedbackList');//反馈列表
Route::post('/inquiryQuoteLog/addFeedback', 'InquiryDataController@addFeedback');//新增反馈
Route::post('/inquiryQuoteLog/handleFeedback', 'InquiryDataController@handleFeedback');//处理反馈


Route::get('testApi', 'IndexController@testApi');

//价格助手
Route::post('/priceAssistant/getGoodsExtraCharge', 'PriceAssistantController@getGoodsExtraCharge'); //获取商品附加费用


