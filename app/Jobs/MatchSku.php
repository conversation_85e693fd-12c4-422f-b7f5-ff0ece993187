<?php

namespace App\Jobs;

use App\Http\Models\BomModel;
use App\Http\Models\BomItemsModel;
use Illuminate\Bus\Queueable;
use App\Http\Services\BomMatchService;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Support\Facades\Log;
use App\Http\Queue\RabbitQueueModel;

class MatchSku implements ShouldQueue
// class MatchSku
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $bom_id;
    protected $bom_items_ids;
    protected $supplier_ids;
    /**
     * Create a new job instance.
     *
     * @param int $bom_id BOM ID
     * @param array $bom_items_ids BOM items IDs
     * @param array $supplier_ids 供应商ids
     * @return void
     */
    public function __construct($bom_id, $bom_items_ids = [], $supplier_ids = [])
    {
        $this->bom_id = $bom_id;
        $this->bom_items_ids = $bom_items_ids;
        $this->supplier_ids = $supplier_ids;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $bom_id = $this->bom_id;
        if (!empty($bom_id)) {
            $bom_info = BomModel::find($bom_id);
            if (!$bom_info) {
                Log::error('MatchSku job: BOM not found', ['bom_id' => $bom_id]);
                return;
            }

            $bom_items = BomItemsModel::where(['bom_id' => $bom_id, 'is_del' => 0])->get();
        } else {
            $bom_items = BomItemsModel::whereIn('id', $this->bom_items_ids)->get();
        }
        if ($bom_items->isEmpty()) {
            Log::info('MatchSku job: No BOM items found', ['bom_id' => $bom_id]);
            return;
        }
        foreach ($bom_items as $bom_item) {
            $this->processItemsChunk($bom_item, $this->supplier_ids);
        }

        //还要去进行匹配后是否生成询价,没法前置,因为匹配完成可能数据还没有入库
        BomMatchService::addInquiryAfterMatch($bom_id);
    }

    /**
     * Process a chunk of BOM items
     *
     * @param \Illuminate\Support\Collection $items
     * @return void
     */
    protected function processItemsChunk($item, $supplier_ids)
    {
        $request_data = [];

        $supplier_ids = !empty($supplier_ids) ? $supplier_ids : (!empty($item['supplier_ids']) ? explode(',', $item['supplier_ids']) : []);
        $supplier_ids = array_map('intval', $supplier_ids);
        $item_data = [
            'goods_name' => $item->goods_name,
            'standard_brand_name' => $item->brand_name,
            'amount' => $item->inquiry_number,
        ];
        if (!empty($supplier_ids)) {
            $item_data['supplier_ids'] = $supplier_ids;
        }

        $request_data[] = $item_data;


        // Skip if no valid items to process
        if (empty($request_data)) {
            return;
        }
        // Call the match service
        $match_results = BomMatchService::mathSku($request_data);
        // Process results and update BOM items
        if (!empty($match_results)) {
            $this->updateBomItems($item, $match_results);
        } else {
            BomItemsModel::updateById([
                'match_status' => BomMatchService::MATCH_STATUS_FAIL,
                'select_bom_match' => 0,
                'update_time' => time()
            ], $item->id);
        }
    }

    /**
     * Update BOM items with match results
     *
     * @param \Illuminate\Support\Collection $items
     * @param array $match_results
     * @return void
     */
    protected function updateBomItems($item, $match_results)
    {
        // if (!isset($match_results[$index])) {
        //     BomItemsModel::updateById([
        //         'match_status' => BomMatchService::MATCH_STATUS_FAIL,
        //         'update_time' => \time(),
        //     ], $item->id);
        //     continue;
        // }
        $result = $match_results;
        // Get main goods_id
        $goods_id = $result['goods_info']['goods_id'] ?? 0;

        // Get other goods_ids
        $other_goods_ids = [];
        if (isset($result['other_selection']) && is_array($result['other_selection'])) {
            foreach ($result['other_selection'] as $selection) {

                if (isset($selection['goods_info']['goods_id'])) {
                    $other_goods_ids[] = $selection['goods_info']['goods_id'];
                }else{
                    if (isset($selection['goods_info'])) {
                        $other_goods_ids[] = $selection['goods_info'];
                    }
                }
            }
        }
        // Update BOM item
        $update_data = [
            'match_status' => BomMatchService::MATCH_STATUS_OK,
            'goods_id' => $goods_id,
            'select_bom_match' => $goods_id ? 1 : 0,
            //匹配到数据的,强制改成用户选择不询价,因为不需要了
            'is_inquiry' => BomItemsModel::IS_INQUIRY_NO,
            'other_goods_id' => !empty($other_goods_ids) ? implode(',', array_unique($other_goods_ids)) : '',
            'update_time' => time()
        ];
        BomItemsModel::updateById($update_data, $item->id);
    }
}
