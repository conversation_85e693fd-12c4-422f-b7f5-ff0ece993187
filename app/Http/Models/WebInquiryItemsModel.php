<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;

class WebInquiryItemsModel extends Model
{
    protected $connection = 'rfq';

    protected $table = 'web_inquiry_items';

    protected $primaryKey = 'id';

    public $timestamps = false;

    const STATUS_UNCONFIRMED = 1; //  未确认报价
    const STATUS_CONFIRMED = 2; // 已确认报价
    const STATUS_ORDERED = 3; // 已成单


    public static function getStatusCountsByInquiryIds(array $web_inquiry_ids): array
    {
        return self::select(
            'web_inquiry_id',
            DB::raw('COUNT(*) as total_item_count'),
            DB::raw("SUM(CASE WHEN item_status = 1 THEN 1 ELSE 0 END) as unconfirmed_count"),
            DB::raw("SUM(CASE WHEN item_status = 2 THEN 1 ELSE 0 END) as confirmed_count"),
            DB::raw("SUM(CASE WHEN item_status = 3 THEN 1 ELSE 0 END) as ordered_count")
        )
            ->whereIn('web_inquiry_id', $web_inquiry_ids)
            ->groupBy('web_inquiry_id')
            ->get()
            ->keyBy('web_inquiry_id')
            ->toArray();
    }

    // 创建网站询价单明细
    public static function createWebInquiryItem($add_web_inquiry_item_info)
    {
        return self::insertGetId($add_web_inquiry_item_info);
    }

    //获取网站询价单明细信息
    public static function getWebInquiryItemById($web_inquiry_item_id)
    {
        $res = self::where(['id' => $web_inquiry_item_id])->first();
        return $res ? $res->toArray() : [];
    }

    // 根据网站询价id，获取网站询价明细列表
    public static function getListByWebInquiryId($web_inquiry_id)
    {
        $res = self::where('web_inquiry_id', $web_inquiry_id)->get();
        return $res ? $res->toArray() : [];
    }

    // 根据网站询价明细ids，获取网站询价明细列表
    public static function getListByIds($web_inquiry_item_ids)
    {
        $res = self::whereIn('id', $web_inquiry_item_ids)->get();
        return $res ? $res->toArray() : [];
    }

    public static function updateById($data, $id)
    {
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }
        return self::where("id", $id)->update($data);
    }

    public static function setConfirmed($web_inquiry_item_ids)
    {
        return self::whereIn("id", $web_inquiry_item_ids)->where('item_status',
            self::STATUS_UNCONFIRMED)->update(['item_status' => self::STATUS_CONFIRMED]);
    }

    public static function getRelaWebInquiryIdsByGoodsName($goods_name)
    {
        return self::query()
            ->where('goods_name', 'like', '%' . $goods_name . '%')
            ->distinct()
            ->pluck('web_inquiry_id')
            ->toArray();
    }


}
