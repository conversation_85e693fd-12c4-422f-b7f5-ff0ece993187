<?php

namespace App\Http\Models\Cube;

use Illuminate\Database\Eloquent\Model;

class OtherFeeRuleModel extends Model
{
    public $connection = 'cube_v2';

    protected $table = 'other_fee_rule';

    protected $primaryKey = 'id';

    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = -1;

    // 费用配置方式
    const CONFIG_TYPE_ALL = 1; // 应用全渠道商品
    const CONFIG_TYPE_PACKAGE = 2; // 按包装方式匹配

    // 获取渠道运费配置
    public static function getOtherFeeListByChannelIds($channel_ids)
    {
        $res = self::whereIn('supplier_id', $channel_ids)->where('status', self::STATUS_ENABLE)->get();
        return $res ? $res->toArray() : [];
    }

}
