<?php

namespace App\Http\Models\Cube;

use Illuminate\Database\Eloquent\Model;

class ShippingRuleModel extends Model
{
    public $connection = 'cube_v2';

    protected $table = 'shipping_rule';

    protected $primaryKey = 'id';

    public $timestamps = false;

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = -1;

    // 配置方式
    const TYPE_FIXED = 1; // 固定金额
    const TYPE_WEIGHT = 2; // 按重量计算

    // 获取渠道运费配置
    public static function getShoppingRuleListByChannelIds($channel_ids)
    {
        $res = self::whereIn('supplier_id', $channel_ids)->where('status', self::STATUS_ENABLE)->get();
        return $res ? $res->toArray() : [];
    }

}
