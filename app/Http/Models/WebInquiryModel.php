<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class WebInquiryModel extends Model
{
    protected $connection = 'rfq';

    protected $table = 'web_inquiry';

    protected $primaryKey = 'web_inquiry_id';

    public $timestamps = false;

    const DELIVERY_PALCE_MAINLAND = 1;  // 交货地点 1：内地 2：香港

    const DELIVERY_PALCE_HONGKONG = 2;  // 交货地点 1：内地 2：香港

    const DELIVERY_PLACE_MAP = [
        self::DELIVERY_PALCE_MAINLAND => '大陆',
        self::DELIVERY_PALCE_HONGKONG => '香港',
    ];

    const STATUS_ENABLE = 1;  //1:启用

    //获取网站询价单
    public static function getWebInquiryBySn($web_inquiry_sn)
    {
        $res = self::where(['web_inquiry_sn' => $web_inquiry_sn])->first();
        return $res ? $res->toArray() : [];
    }

    //获取网站询价单
    public static function getWebInquiryById($web_inquiry_id)
    {
        $res = self::where(['web_inquiry_id' => $web_inquiry_id])->first();
        return $res ? $res->toArray() : [];
    }

    // 创建网站询价单
    public static function createWebInquiry($add_web_inquiry_info)
    {
        return self::insertGetId($add_web_inquiry_info);
    }

    // 获取列表
    public static function getListByWhere($where = [], $page = 1, $limit = 10, $extra_where = [], $desc = 'desc')
    {
        $query = self::select('*');
        // 获取销售员名下询价单
        if (isset($extra_where['sale_uids']) && $extra_where['sale_uids']) {
            $query->whereIn('create_uid', $extra_where['sale_uids']);
        }
        if (isset($extra_where['in_web_inquiry_ids'])) {
            $query->whereIn('web_inquiry_id', $extra_where['in_web_inquiry_ids']);
        }
        return $query->where($where)->orderBy('web_inquiry_id', $desc)->paginate($limit, ['*'], 'p', $page)->toArray();
    }

    // 更新没有设置销售员的网站询价
    public static function updateByUserId($data, $user_id)
    {
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }
        return self::where("user_id", $user_id)->where('create_uid', 0)->update($data);
    }

    public static function updateById($data, $web_inquiry_id)
    {
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }
        return self::where("web_inquiry_id", $web_inquiry_id)->update($data);
    }

}
