<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class BomManualModel extends Model
{
    protected $connection = 'rfq';

    protected $table = 'bom_manual';

    protected $primaryKey = 'id';

    public $timestamps = false;

    // 状态常量
    const STATUS_UNMATCHED = 1;     // 未匹配
    const STATUS_MATCHED = 2;       // 已匹配
    const STATUS_PARTIAL_MATCHED = 3; // 部分匹配

    // 是否删除常量
    const IS_DEL_NO = 0;   // 未删除
    const IS_DEL_YES = 1;  // 已删除

    /**
     * 根据ID获取记录
     */
    public static function getById($id)
    {
        $res = self::find($id);
        return ($res) ? $res->toArray() : [];
    }

    /**
     * 根据条件获取列表（分页）
     */
    public static function getListByWhere($where = [], $page = 1, $limit = 10)
    {
        return self::where($where)
            ->where('is_del', self::IS_DEL_NO)
            ->orderBy('id', 'desc')
            ->paginate($limit, ['*'], 'p', $page)
            ->toArray();
    }

    /**
     * 添加记录
     */
    public static function addRecord($data)
    {
        if (!isset($data['create_time'])) {
            $data['create_time'] = time();
        }
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }
        return self::insertGetId($data);
    }

    /**
     * 根据ID更新记录
     */
    public static function updateById($id, $update)
    {
        $update['update_time'] = time();
        return self::where("id", $id)->update($update);
    }

    /**
     * 检查BOM编号是否存在
     */
    public static function existsBomSn($bom_sn)
    {
        return self::where('bom_sn', $bom_sn)
            ->where('is_del', self::IS_DEL_NO)
            ->exists();
    }

    /**
     * 软删除记录
     */
    public static function softDeleteById($id, $update_uid, $update_name)
    {
        return self::updateById($id, [
            'is_del' => self::IS_DEL_YES,
            'update_uid' => $update_uid,
            'update_name' => $update_name
        ]);
    }

    /**
     * 获取状态名称
     */
    public static function getStatusName($status)
    {
        $statusMap = [
            self::STATUS_UNMATCHED => '未匹配',
            self::STATUS_MATCHED => '已匹配',
            self::STATUS_PARTIAL_MATCHED => '部分匹配'
        ];

        return $statusMap[$status] ?? '未知状态';
    }
}
