<?php

namespace App\Http\Models;

use Illuminate\Database\Eloquent\Model;

class WebInquiryConfirmedQuoteModel extends Model
{
    protected $connection = 'rfq';

    protected $table = 'web_inquiry_confirmed_quote';

    protected $primaryKey = 'id';

    public $timestamps = false;

    const STATUS_UNPUSHED = 0; // 未推送客户
    const STATUS_PUSHED = 1;  //已推送客户

    //获取确认信息
    public static function getConfirmInfoByWebInquiryItemId($web_inquiry_items_id)
    {
        $res = self::where(['web_inquiry_items_id' => $web_inquiry_items_id])->first();
        return $res ? $res->toArray() : [];
    }

    // 根据网站询价id，获取网站询价确认明细列表
    public static function getListByWebInquiryId($web_inquiry_id)
    {
        $res = self::where('web_inquiry_id', $web_inquiry_id)->get();
        return $res ? $res->toArray() : [];
    }

    // 根据网站询价明细ids，获取网站询价确认明细列表
    public static function getListByWebInquiryItemIds($web_inquiry_item_ids)
    {
        $res = self::whereIn('web_inquiry_items_id', $web_inquiry_item_ids)->get();
        return $res ? $res->toArray() : [];
    }

    // 创建询价确认信息
    public static function createConfirmItem($add_confirm_info)
    {
        return self::insertGetId($add_confirm_info);
    }

    public static function updateById($data, $id)
    {
        if (!isset($data['update_time'])) {
            $data['update_time'] = time();
        }
        return self::where("id", $id)->update($data);
    }

    public static function getPushedCountByWebInquiryId($web_inquiry_id)
    {
        return self::where(['web_inquiry_id' => $web_inquiry_id, 'confirmed_status' => self::STATUS_PUSHED])->count();
    }

}
