<?php

namespace App\Http\Services;


class ChannelShippingPriceService
{
    const CURRENCY_TYPE_RMB = 1; //币种-人民币
    const CURRENCY_TYPE_US = 2;  //币种-美元

    const CURRENCY_TYPE_HK = 3;  //币种-港币

    // 获取运费-按订单金额配置
    public static function getShippingAmountByTotalAmount(
        $order_total_amount,
        $delivery_place,
        $us_exchange_rate,
        $ruleData
    ) {
        $shipping_amount = 0;
        // 如果交货地是大陆，那么用配置的人民币价格，如果有按汇率转换，那么使用配置的美元价格，在转换人民币
        if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
            $has_free_shipping_amount = (isset($ruleData['free_shipping_cny_amount']) && $ruleData['free_shipping_cny_amount']) ? true : false;
            $has_shipping_amount = (isset($ruleData['shipping_cny_amount']) && $ruleData['shipping_cny_amount']) ? true : false;
            if ($has_free_shipping_amount && $has_shipping_amount) {
                if ($order_total_amount >= $ruleData['free_shipping_cny_amount']) {
                    $shipping_amount = 0;
                } else {
                    $shipping_amount = $ruleData['shipping_cny_amount'];
                }
            }

            if (isset($ruleData['is_change_rate_cny']) && ($ruleData['is_change_rate_cny'] == 1)) {
                $has_free_shipping_amount = (isset($ruleData['free_shipping_usd_amount']) && $ruleData['free_shipping_usd_amount']) ? true : false;
                $has_shipping_amount = (isset($ruleData['shipping_usd_amount']) && $ruleData['shipping_usd_amount']) ? true : false;
                if ($has_free_shipping_amount && $has_shipping_amount) {
                    $free_shipping_cny_amount = PriceService::getBaseCurrencyPrice($ruleData['free_shipping_usd_amount'],
                        $us_exchange_rate);
                    $shipping_cny_amount = PriceService::getBaseCurrencyPrice($ruleData['shipping_usd_amount'],
                        $us_exchange_rate);
                    if ($order_total_amount >= $free_shipping_cny_amount) {
                        $shipping_amount = 0;
                    } else {
                        $shipping_amount = $shipping_cny_amount;
                    }
                }
            }
        } else {
            $has_free_shipping_amount = (isset($ruleData['free_shipping_usd_amount']) && $ruleData['free_shipping_usd_amount']) ? true : false;
            $has_shipping_amount = (isset($ruleData['shipping_usd_amount']) && $ruleData['shipping_usd_amount']) ? true : false;
            if ($has_free_shipping_amount && $has_shipping_amount) {
                if ($order_total_amount >= $ruleData['free_shipping_usd_amount']) {
                    $shipping_amount = 0;
                } else {
                    $shipping_amount = $ruleData['shipping_usd_amount'];
                }
            }

            if (isset($ruleData['is_change_rate_usd']) && ($ruleData['is_change_rate_usd'] == 1)) {
                $has_free_shipping_amount = (isset($ruleData['free_shipping_cny_amount']) && $ruleData['free_shipping_cny_amount']) ? true : false;
                $has_shipping_amount = (isset($ruleData['shipping_cny_amount']) && $ruleData['shipping_cny_amount']) ? true : false;
                if ($has_free_shipping_amount && $has_shipping_amount) {
                    $free_shipping_usd_amount = PriceService::getPriceByExchangeRate($ruleData['free_shipping_cny_amount'],
                        $us_exchange_rate);
                    $shipping_usd_amount = PriceService::getPriceByExchangeRate($ruleData['shipping_cny_amount'],
                        $us_exchange_rate);
                    if ($order_total_amount >= $free_shipping_usd_amount) {
                        $shipping_amount = 0;
                    } else {
                        $shipping_amount = $shipping_usd_amount;
                    }
                }
            }
        }
        return $shipping_amount;
    }

    // 获取运费-按订单金额阶梯配置
    public static function getShippingAmountByLadderTotalAmount(
        $order_total_amount,
        $delivery_place,
        $us_exchange_rate,
        $ruleData
    ) {
        $shipping_amount = 0;
        // 如果交货地是大陆，那么用配置的人民币价格，如果有按汇率转换，那么使用配置的美元价格，在转换人民币
        if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
            if ($ruleData['base_cny_amount']) {
                $shipping_amount = $ruleData['base_cny_amount'];
                $remain_cny_amount = bcsub($order_total_amount, $ruleData['ladder_cny_amount'], 2);
                if (($remain_cny_amount > 0) && $ruleData['ladder_cny_amount'] && $ruleData['ladder_additional_cny_amount']) {
                    $ladder_times = ceil(bcdiv($remain_cny_amount, $ruleData['ladder_cny_amount'], 1));
                    $shipping_amount = $shipping_amount + ($ruleData['ladder_additional_cny_amount'] * $ladder_times);
                }
            } else {
                if (isset($ruleData['ladder_is_change_rate_cny']) && ($ruleData['ladder_is_change_rate_cny'] == 1)) {
                    if ($ruleData['base_usd_amount']) {
                        $shipping_amount = PriceService::getBaseCurrencyPrice($ruleData['base_usd_amount'],
                            $us_exchange_rate);
                        $ladder_cny_amount = PriceService::getBaseCurrencyPrice($ruleData['ladder_usd_amount'],
                            $us_exchange_rate);
                        $remain_cny_amount = bcsub($order_total_amount, $ladder_cny_amount, 2);
                        if (($remain_cny_amount > 0) && $ruleData['ladder_usd_amount'] && $ruleData['ladder_additional_usd_amount']) {
                            $ladder_additional_cny_amount = PriceService::getBaseCurrencyPrice($ruleData['ladder_additional_usd_amount'],
                                $us_exchange_rate);
                            $ladder_times = ceil(bcdiv($remain_cny_amount, $ladder_cny_amount, 1));
                            $shipping_amount = $shipping_amount + ($ladder_additional_cny_amount * $ladder_times);
                        }
                    }
                }
            }
        } else {
            if ($ruleData['base_usd_amount']) {
                $shipping_amount = $ruleData['base_usd_amount'];
                $remain_usd_amount = bcsub($order_total_amount, $ruleData['ladder_usd_amount'], 2);
                if (($remain_usd_amount > 0) && $ruleData['ladder_usd_amount'] && $ruleData['ladder_additional_usd_amount']) {
                    $ladder_times = ceil(bcdiv($remain_usd_amount, $ruleData['ladder_usd_amount'], 1));
                    $shipping_amount = $shipping_amount + ($ruleData['ladder_additional_usd_amount'] * $ladder_times);
                }
            } else {
                if (isset($ruleData['ladder_is_change_rate_usd']) && ($ruleData['ladder_is_change_rate_usd'] == 1)) {
                    if ($ruleData['base_cny_amount']) {
                        $shipping_amount = PriceService::getPriceByExchangeRate($ruleData['base_cny_amount'],
                            $us_exchange_rate);
                        $ladder_usd_amount = PriceService::getPriceByExchangeRate($ruleData['ladder_cny_amount'],
                            $us_exchange_rate);
                        $remain_usd_amount = bcsub($order_total_amount, $ladder_usd_amount, 2);
                        if (($remain_usd_amount > 0) && $ruleData['ladder_cny_amount'] && $ruleData['ladder_additional_cny_amount']) {
                            $ladder_additional_usd_amount = PriceService::getPriceByExchangeRate($ruleData['ladder_additional_cny_amount'],
                                $us_exchange_rate);
                            $ladder_times = ceil(bcdiv($remain_usd_amount, $ladder_usd_amount, 1));
                            $shipping_amount = $shipping_amount + ($ladder_additional_usd_amount * $ladder_times);
                        }
                    }
                }
            }
        }
        return $shipping_amount;
    }

    // 获取运费-按订单型号数量配置
    public static function getShippingAmountByTotalNumber(
        $order_total_number,
        $delivery_place,
        $us_exchange_rate,
        $ruleData
    ) {
        $shipping_amount = 0;
        if (isset($ruleData['sku_count']) && $ruleData['sku_count']) {
            foreach ($ruleData['sku_count'] as $countRule) {
                if (($order_total_number >= $countRule['min_count']) && (($order_total_number <= $countRule['max_count']) || empty($countRule['max_count']))) {
                    // 如果交货地是大陆，那么用配置的人民币价格，如果有按汇率转换，那么使用配置的美元价格，在转换人民币
                    if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                        if ($countRule['cny_fee']) {
                            $shipping_amount = $countRule['cny_fee'];
                        } else {
                            if (isset($ruleData['count_is_change_rate_cny']) && ($ruleData['count_is_change_rate_cny'] == 1)) {
                                if ($countRule['usd_fee']) {
                                    $shipping_amount = PriceService::getBaseCurrencyPrice($countRule['usd_fee'],
                                        $us_exchange_rate);
                                }
                            }
                        }
                    } else {
                        if ($countRule['usd_fee']) {
                            $shipping_amount = $countRule['usd_fee'];
                        } else {
                            if (isset($ruleData['count_is_change_rate_usd']) && ($ruleData['count_is_change_rate_usd'] == 1)) {
                                if ($countRule['cny_fee']) {
                                    $shipping_amount = PriceService::getPriceByExchangeRate($countRule['cny_fee'],
                                        $us_exchange_rate);
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
        return $shipping_amount;
    }

    // 获取运费-按订单型号重量
    public static function getShippingAmountByTotalWeightKg(
        $order_total_weight_kg,
        $delivery_place,
        $us_exchange_rate,
        $ruleData
    ) {
        $shipping_amount = 0;
        if (isset($ruleData['sku_weight']) && $ruleData['sku_weight']) {
            foreach ($ruleData['sku_weight'] as $countRule) {
                if (($order_total_weight_kg >= $countRule['min_weight']) && (($order_total_weight_kg < $countRule['max_weight']) || empty($countRule['max_weight']))) {
                    // 如果交货地是大陆，那么用配置的人民币价格，如果有按汇率转换，那么使用配置的美元价格，在转换人民币
                    if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                        if ($countRule['cny_fee']) {
                            $shipping_amount = $countRule['cny_fee'];
                        } else {
                            if (isset($ruleData['weight_is_change_rate_cny']) && ($ruleData['weight_is_change_rate_cny'] == 1)) {
                                if ($countRule['usd_fee']) {
                                    $shipping_amount = PriceService::getBaseCurrencyPrice($countRule['usd_fee'],
                                        $us_exchange_rate);
                                }
                            }
                        }
                    } else {
                        if ($countRule['usd_fee']) {
                            $shipping_amount = $countRule['usd_fee'];
                        } else {
                            if (isset($ruleData['weight_is_change_rate_usd']) && ($ruleData['weight_is_change_rate_usd'] == 1)) {
                                if ($countRule['cny_fee']) {
                                    $shipping_amount = PriceService::getPriceByExchangeRate($countRule['cny_fee'],
                                        $us_exchange_rate);
                                }
                            }
                        }
                    }
                    break;
                }
            }
        }
        return $shipping_amount;
    }

}
