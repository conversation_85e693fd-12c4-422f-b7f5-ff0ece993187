<?php

namespace App\Http\Services;

use App\Http\Caches\SkuCache;
use App\Http\Models\BomModel;
use App\Http\Models\InquiryModel;
use App\Http\Models\BomItemsModel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use App\Http\Queue\RabbitQueueModel;
use Illuminate\Support\Facades\Http;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\Spu\SupplierModel;
use App\Http\Models\QuoteSelectedModel;
use App\Http\Models\InquiryRelaBomModel;
use App\Http\Models\CmsUserIntraCodeModel;
use App\Exceptions\InvalidRequestException;

class BomMatchService
{

    // bom匹配状态(1是匹配成功,0是匹配中,-1是匹配失败)
    const MATCH_STATUS_PENDING = 0;
    const MATCH_STATUS_OK = 1;
    const MATCH_STATUS_FAIL = -1;

    static public $matchStatusCn = [
        0 => '待匹配',
        1 => '匹配成功',
        -1 => '匹配失败',
    ];

    static public $abilityLevelCn = [
        0 => '弱履约',
        1 => '强履约B',
        2 => '强履约A',
    ];

    /**
     * 根据信息去匹配bom搜索服务获取对应的sku，返回匹配结果
     *
     * 使用POST方法，将参数作为goods_list字段发送
     *
     * @param array $params 请求参数数组，将作为goods_list字段的值
     * @return array 匹配结果
     */
    public static function mathSku($params)
    {
        try {
            $url = config('website.open_platform_domain') . '/bom/matchSku';
            $response = Http::post($url, ['goods_list' => $params]);
            $result = $response->json();
            if (!isset($result['code']) || $result['code'] !== 0) {
                Log::error('BomMatchService::mathSku API request failed', [
                    'params' => $params,
                    'response' => $result
                ]);
                return [];
            }

            return $result['data'][0] ?? [];
        } catch (\Exception $e) {
            Log::error('BomMatchService::mathSku exception', [
                'params' => $params,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return [];
        }
    }

    /**
     * 更新bom_items表的goods_id
     *
     * @param int $bom_items_id bom_items表的id
     * @param int $goods_id goods表的id
     * @return void
     */
    public static function updateBomItemsGoodsId($bom_items_id, $goods_id)
    {
        $result = BomItemsModel::where('id', $bom_items_id)->update(['goods_id' => $goods_id, 'update_time' => time()]);
        return $result;
    }

    //获取数量统计
    public static function getBomMatchCount($bom_id)
    {
        $isMatchStatus = BomItemsModel::where(['bom_id' => $bom_id, 'is_del' => 0])
            ->pluck('match_status')->toArray();
        $all = count($isMatchStatus, COUNT_NORMAL);
        $match = count(array_filter($isMatchStatus, function ($value) {
            return $value == self::MATCH_STATUS_OK;
        }));
        $fail = count(array_filter($isMatchStatus, function ($value) {
            return $value == self::MATCH_STATUS_FAIL;
        }));
        $pending = $all - $match - $fail;
        return ['all' => $all, 'match' => $match, 'fail' => $fail, 'pending' => $pending];
    }

    //设置是否选择智能询价
    public static function updateSelectBomMatch($bom_item_id, $select_bom_match)
    {
        return BomItemsModel::where('id', $bom_item_id)->update([
            'update_time' => time(),
            'select_bom_match' => $select_bom_match,
        ]);
    }

    public static function addInquiryAfterMatch($bomId)
    {
        $RabbitQueueModel = new RabbitQueueModel();
        $bomStatus = BomModel::where('id', $bomId)->value('bom_status');
        if ($bomStatus == BomModel::STATUS_TEMP_SAVE) {
            return;
        }

        $bomItems = BomItemsModel::where(['bom_id' => $bomId, 'is_del' => 0])->get();
        foreach ($bomItems as $bomItem) {
            $goodsId = $bomItem->goods_id;
            if (empty($goodsId)) {
                continue;
            }
            $goods_info = (new SkuCache())->getSkuInfo($goodsId);
            if (empty($goods_info)) {
                continue;
            }
            $goods_info = json_decode($goods_info, true);
            //是否询价为“否”，渠道为猎芯专营，则生成一条询价单
            if ($bomItem->is_inquiry == BomItemsModel::IS_INQUIRY_NO && $goods_info['supplier_id'] == SkuService::CHANNEL_LIEXIN_ZHUANYING) {
                $queue_data = [
                    'bom_item_id' => $bomItem->id,
                ];
                $RabbitQueueModel->insertQueue('/queue/bom/addInquiry', $queue_data, RabbitQueueModel::QUEUE_FRQ);
            }
        }
    }

    public static function exportBom($bom_id, $bom_items_ids)
    {
        // 获取BOM详情数据
        $bom_items = [];
        $bom_info = [];

        if (!empty($bom_id)) {
            // 如果传了bom_id，获取该BOM下的所有明细
            $bom_info = BomModel::getById($bom_id);
            if (empty($bom_info)) {
                throw new InvalidRequestException('BOM不存在');
            }
            $bom_items = BomItemsModel::getListByBomId($bom_id);
        } else {
            // 如果传了bom_items_ids，获取这些明细
            if (is_string($bom_items_ids)) {
                $bom_items_ids = explode(',', $bom_items_ids);
                $bom_items_ids = array_values(array_filter($bom_items_ids));
            }

            $bom_items = BomItemsModel::getListByIds($bom_items_ids);
            if (!empty($bom_items)) {
                $bom_id = $bom_items[0]['bom_id'];
                $bom_info = BomModel::getById($bom_id);
            }
        }

        if (empty($bom_items)) {
            throw new InvalidRequestException("没有找到BOM明细数据");
        }

        // 获取询价单信息
        $bom_items_ids = array_column($bom_items, 'id');
        $inquiry_item_map = [];
        $inquiry_ids = [];
        $inquiry_item_ids = [];

        // 获取询价单信息
        $inquiry_rela_bom_list = InquiryRelaBomModel::getListByBomItemIds($bom_items_ids);
        if (!empty($inquiry_rela_bom_list)) {
            $inquiry_item_ids = array_column($inquiry_rela_bom_list, 'inquiry_item_id');
            $inquiry_item_list = InquiryItemModel::getListByIds($inquiry_item_ids);

            foreach ($inquiry_item_list as $inquiry_item) {
                $inquiry_ids[] = $inquiry_item['inquiry_id'];
                $bom_item_id = 0;
                foreach ($inquiry_rela_bom_list as $rela) {
                    if ($rela['inquiry_item_id'] == $inquiry_item['id']) {
                        $bom_item_id = $rela['bom_item_id'];
                        break;
                    }
                }
                if ($bom_item_id) {
                    $inquiry_item_map[$bom_item_id] = $inquiry_item;
                }
            }
        }

        // 获取询价单号
        $inquiry_map = [];
        if (!empty($inquiry_ids)) {
            $inquiry_list = InquiryModel::getListByIds($inquiry_ids);
            foreach ($inquiry_list as $inquiry) {
                $inquiry_map[$inquiry['id']] = $inquiry;
            }
        }
        $goods_info_list = SkuService::getGoodsInfoByGoodsIds(array_column($bom_items, 'goods_id'));
        // 处理导出数据
        foreach ($bom_items as $key => &$item) {
            // 处理截止时间
            $bom_items[$key]['deadline_time'] = $item['deadline_time'] ?? 0;

            // 询价信息
            $inquiry_item = $inquiry_item_map[$item['id']] ?? [];
            $inquiry_id = $inquiry_item['inquiry_id'] ?? 0;
            $inquiry = $inquiry_map[$inquiry_id] ?? [];
            $item['inquiry_items_ids'] = $inquiry_item['id'] ?? 0;
            //取BOM明细报价来源（即无询价单展示的是智能匹配SKU对应的履约级别；有询价单则显示的是该询价单选中报价的“报价来源”，未选中则为空）
            //bom匹配信息
            $item['other_goods_id'] = !empty($item['other_goods_id']) ? explode(',', $item['other_goods_id']) : [];
            if ($item['goods_id']) {
                $item['other_goods_id'][] = $item['goods_id'];
            }
            $item['other_goods_id'] = array_unique($item['other_goods_id']);
            $item['other_goods_count'] = count($item['other_goods_id']);
            $goods_info = $goods_info_list[$item['goods_id']] ?? [];
            //获取价格
            if ($item['goods_id']) {
                $price = SkuService::getSinglePrice($bom_info['delivery_place'], $item['inquiry_number'], $goods_info);
            } else {
                $price = [];
            }
            $item['encoded'] = $goods_info['encoded'] ?? '';
            $item['price_in_tax'] = $price['price'] ?? 0;
            $item['price_without_tax'] = $price['price_without_tax'] ?? 0;
            $item['currency_symbol'] = $price['currency_symbol'] ?? '';
            $item['supplier_name'] = $goods_info['supplier_name'] ?? '';
            $item['ability_level'] = $goods_info['ability_level'] ?? 0;
            $item['supplier_id'] = $goods_info['supplier_id'] ?? 0;
            $item['batch'] = $goods_info['batch'] ?? '';

            $item['ability_level_cn'] = $item['goods_id'] ? (isset($item['ability_level']) ? BomMatchService::$abilityLevelCn[$item['ability_level']] : '') : '';
            $item['match_status_cn'] = BomMatchService::$matchStatusCn[$item['match_status']] ?? '';
        }
        $supplier_purchase_uid_list = SupplierModel::getPurchaseUidBySupplierIds(array_column($bom_items, 'supplier_id'));
        $allEncodedList = array_merge(array_column($bom_items, 'encoded'), $supplier_purchase_uid_list);
        $purchase_user_list = CmsUserIntraCodeModel::getAdminUsersByCodeIds($allEncodedList);
        //报价信息
        $quoteList = BomService::matchQuote(array_column($bom_items, 'id'));
        foreach ($bom_items as $key => &$item) {
            $item['has_quote'] = 0;
            $item['quote_encoded_name'] = $purchase_user_list[$item['encoded']]['name'] ?? '';
            $match_quote = $quoteList[$item['inquiry_items_ids']] ?? [];
            $item['quote_ability_level_cn'] = $item['ability_level_cn'];
            if (!empty($match_quote)) {
                $item['quote_id'] = $match_quote['id'];
                $item['quote_brand_name'] = $match_quote['brand_name'];
                $item['quote_goods_name'] = $match_quote['goods_name'];
                $item['quote_encoded_name'] = $match_quote['create_name'];
                //$item['supplier_name'] = $match_quote['supplier_name'];
                $item['quote_ability_level_cn'] = $match_quote['source'];
                $item['quote_number'] = $match_quote['quote_number'];
                $item['quote_price_in_tax'] = $match_quote['price_tax'];
                $item['quote_price_without_tax'] = $match_quote['price_origin'];
                $item['quote_currency_symbol'] = $match_quote['currency_symbol'];
                $item['quote_delivery_time'] = $match_quote['delivery_time'];
                $item['quote_batch'] = $match_quote['batch'];
                $item['quote_remark'] = $match_quote['remark'];
                $item['quote_create_name'] = $match_quote['create_name'];
                $item['quote_expire_time'] = $match_quote['expire_time'] ? date('Y-m-d H:i:s', $match_quote['expire_time']) : '';
                $item['quote_supplier_name'] = $item['supplier_name'];
                $item['quote_ability_level_cn'] = $match_quote['source'];
                $item['has_quote'] = 1;
            } else {
                if (!empty($item['goods_id'])) {
                    $item['quote_brand_name'] = $item['brand_name'];
                    $item['quote_goods_name'] = $item['goods_name'];
                    $item['supplier_name'] = $item['supplier_name'];
                    $item['quote_ability_level_cn'] = $item['ability_level_cn'];
                    $item['quote_number'] = $item['inquiry_number'];
                    $item['quote_price_in_tax'] = $item['price_in_tax'];
                    $item['quote_price_without_tax'] = $item['price_without_tax'];
                    $item['quote_currency_symbol'] = $item['currency_symbol'];
                    $item['quote_delivery_time'] = $item['delivery_time'];
                    $item['quote_batch'] = $item['batch'];
                    $item['quote_supplier_name'] = $item['supplier_name'];
                    if ($item['supplier_id'] == 17) {
                        $item['quote_encoded_name'] = $purchase_user_list[$item['encoded']]['name'] ?? '';
                    } else {
                        $codeId = $supplier_purchase_uid_list[$item['supplier_id']] ?? '';
                        $purchase_user = $purchase_user_list[$codeId] ?? [];
                        $item['quote_encoded_name'] = $purchase_user['name'] ?? '';
                    }
                    $item['has_quote'] = 1;
                }
            }
        }

        unset($item);
        // 导出文件名
        $filename = 'BOM详情导出_' . date('YmdHis') . '.xlsx';
        if (!empty($bom_info)) {
            $filename = 'BOM详情_' . ($bom_info['bom_sn'] ?? '') . '_' . date('YmdHis') . '.xlsx';
        }
        // 使用Laravel Excel导出
        return Excel::download(new \App\Exports\BomDetailExport($bom_items, $bom_info), $filename);
    }


    const FRQ_ALL = 3;
    const FRQ_PART = 2;
    const FRQ_NONE = 1;

    //修改网站的询价明细的报价状态
    public static function updateLiexinInquiryItemQuoteStatus($inquiry_item_id)
    {
        $model = DB::connection('web');
        $result = $model->table('inquiry_item')->where('id', $inquiry_item_id)->update([
            'has_frq_quote' => 1,
            'update_time' => time()
        ]);
        if ($result !== false) {
            $inquiryId = $model->table('inquiry_item')->where('id', $inquiry_item_id)->value('inquiry_id');
            //还要去判断整单状态
            $hasQuoteItemsCount = $model->table('inquiry_item')->where('inquiry_id', $inquiryId)->where('has_frq_quote', 1)->count();
            $totalItemsCount = $model->table('inquiry_item')->where('inquiry_id', $inquiryId)->count();
            if ($hasQuoteItemsCount) {
                if ($hasQuoteItemsCount < $totalItemsCount) {
                    //更新整单状态
                    $model->table('inquiry')->where('id', $inquiryId)->update([
                        'frq_status' => self::FRQ_PART,
                        'update_time' => time()
                    ]);
                }

                if ($hasQuoteItemsCount == $totalItemsCount) {
                    //更新整单状态
                    $model->table('inquiry')->where('id', $inquiryId)->update([
                        'frq_status' => self::FRQ_ALL,
                        'update_time' => time()
                    ]);
                }
            }
        }
        return $result;
    }
}
