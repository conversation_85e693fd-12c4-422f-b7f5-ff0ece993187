<?php

namespace App\Http\Services;


use App\Http\Caches\SkuCache;
use App\Http\Models\Cube\OtherFeeRuleModel;
use App\Http\Models\Cube\ShippingRuleModel;
use Illuminate\Support\Facades\Http;

class PriceAssistantService
{

    // 附加费-关税
    const EXTRA_FEE_TYPE_TAX = 1;

    // 附加费-商品产地税
    const EXTRA_FEE_TYPE_LAND = 2;

    // 附加费-对等关税
    const EXTRA_FEE_TYPE_EQUIVALENT = 3;

    // 附加费-商检费用
    const EXTRA_FEE_TYPE_INSPECTION = 4;

    // 附加费-香港清关费
    const EXTRA_FEE_TYPE_HK_CLEARANCE = 5;

    // 附加费-预计渠道费
    const EXTRA_FEE_TYPE_CHANNEL = 6;


    const DELIVERY_PALCE_MAINLAND = 1;  // 交货地点 1：内地 2：香港

    const DELIVERY_PALCE_HONGKONG = 2;  // 交货地点 1：内地 2：香港

    const CHANNEL_LIEXIN_ID = 17;  // 渠道猎芯专营id

    // 获取商品附加费
    public static function getGoodsListWithExtraFee($goods_list, $delivery_place, $extra_fee_types)
    {
        $sku_ids = array_column($goods_list, 'sku_id');
        $goods_id_info_map = [];
        if ($sku_ids) {
            $sku_info_url = config('website.goods_server') . '/synchronization';
            // 根据 goods_id 获取商品信息
            $goods_info_res = Http::get($sku_info_url, [
                'goods_id' => join(',', $sku_ids),
                'show_spu_extra' => 1
            ]);
            $goods_info_list = $goods_info_res->json();
            if ($goods_info_list['errcode'] == 0 && isset($goods_info_list['data']) && $goods_info_list['data']) {
                $goods_id_info_map = $goods_info_list['data'];
            }
        }
        foreach ($goods_list as &$goods_item) {
            $sku_info = $goods_id_info_map[$goods_item['sku_id']] ?? [];
            $goods_item['supplier_code'] = isset($sku_info['canal']) ? $sku_info['canal'] : "";
            $weight_int = isset($sku_info['spu_extra']['weight']) ? $sku_info['spu_extra']['weight'] : 0;
            $weight_kg = rtrim(rtrim(sprintf("%.10f", $weight_int / 1000), '0'), '.');
            $goods_item['weight_kg'] = $weight_kg;
            $goods_item['brand_pack'] = isset($sku_info['spu_extra']['brand_pack']) ? trim($sku_info['spu_extra']['brand_pack']) : "";

            // 设置唯一key，如果没有的话，自定义一个
            if (!isset($goods_item['unique_key'])) {
                $goods_item['unique_key'] = md5($goods_item['goods_name']);
            }
        }
        unset($goods_item);

        // 获取各渠道运费规则
        $shipping_rule_list_map = PriceAssistantService::getChannelShippingRuleMap($goods_list);
        // 渠道其他费用规则
        $other_fee_rule_list_map = PriceAssistantService::getChannelOtherFeeRuleMap($goods_list);
        // 获取各渠道运费
        $channel_shipping_amount_map = PriceAssistantService::getChannelShippingAmountMap($goods_list, $delivery_place);

        $goods_list_with_extra_charge = [];
        $goods_names = array_column($goods_list, 'goods_name');
        $goods_tax_info_map = TaxService::eccnRules($goods_names);
        $total_order_amount = PriceAssistantService::getTotalOrderAmount($goods_list);
        $total_clearance_fee = PriceAssistantService::getTotalClearanceFee($goods_list, $delivery_place);
        $hg_member_count_map = PriceAssistantService::getHgMemberCountMap($goods_list, $goods_tax_info_map);
        $order_total_weight_kg = PriceAssistantService::getTotalOrderWeightKg($goods_list);
        $order_total_number = PriceAssistantService::getTotalOrderNumber($goods_list);

        foreach ($goods_list as $goods_item) {
            $numerator_amount = bcmul($goods_item['goods_number'], $goods_item['goods_price'], 6);
            $goods_tax_info = isset($goods_tax_info_map[$goods_item['goods_name']]) ? $goods_tax_info_map[$goods_item['goods_name']] : [];
            // 商品关税信息
            if (in_array(self::EXTRA_FEE_TYPE_TAX, $extra_fee_types)) {
                $tariff_fee_info = PriceAssistantService::getGoodsTariffFeeInfo($goods_item, $goods_tax_info,
                    $delivery_place);
            } else {
                $tariff_fee_info = [
                    'tariff_fee' => "0",
                    'tariff_fee_detail' => "不计算关税费用",
                ];
            }

            // 商品产地税
            if (in_array(self::EXTRA_FEE_TYPE_LAND, $extra_fee_types)) {
                $land_fee_info = PriceAssistantService::getGoodsLandFeeInfo($goods_item, $goods_tax_info,
                    $delivery_place);
            } else {
                $land_fee_info = [
                    'land_tax_fee' => "0",
                    'land_tax_fee_detail' => "不计算美国产地税",
                ];
            }

            // 对等关税
            if (in_array(self::EXTRA_FEE_TYPE_EQUIVALENT, $extra_fee_types)) {
                $equivalent_fee_info = PriceAssistantService::getGoodsEquivalentFeeInfo($goods_item, $goods_tax_info,
                    $delivery_place);
            } else {
                $equivalent_fee_info = [
                    'equivalent_tax_fee' => "0",
                    'equivalent_tax_fee_detail' => "不计算美国对等关税",
                ];
            }

            // 商检费用
            if (in_array(self::EXTRA_FEE_TYPE_INSPECTION, $extra_fee_types)) {
                $import_inspection_fee_info = PriceAssistantService::getGoodsImportInspectionFeeInfo(
                    $goods_tax_info, $delivery_place, $hg_member_count_map);
            } else {
                $import_inspection_fee_info = [
                    'import_inspection_fee' => "0",
                    'import_inspection_fee_detail' => "不计算商检费",
                ];
            }

            // 香港清关费
            if (in_array(self::EXTRA_FEE_TYPE_HK_CLEARANCE, $extra_fee_types)) {
                $hk_clearance_fee = PriceAssistantService::getShareFee($total_order_amount, $numerator_amount,
                    $total_clearance_fee);
                $hk_clearance_fee_info = [
                    'hk_clearance_fee' => $hk_clearance_fee,
                    'hk_clearance_fee_detail' => "总清关费用：{$total_clearance_fee}, 按明细金额分摊，分摊清关费用：{$hk_clearance_fee}",
                ];
            } else {
                $hk_clearance_fee_info = [
                    'hk_clearance_fee' => "0",
                    'hk_clearance_fee_detail' => "不计算香港清关费",
                ];
            }

            // 预计渠道费
            if (in_array(self::EXTRA_FEE_TYPE_CHANNEL, $extra_fee_types)) {
                //获取渠道运费
                $channel_uni_key = $goods_item['channel_id'];
                if ($goods_item['channel_id'] == PriceAssistantService::CHANNEL_LIEXIN_ID) {
                    $channel_uni_key = "{$goods_item['channel_id']}_{$goods_item['supplier_code']}";
                }
                $shipping_amount = 0;
                $cur_shipping_rule = isset($shipping_rule_list_map[$channel_uni_key]) ? $shipping_rule_list_map[$channel_uni_key] : [];
                $total_shipping_fee = isset($channel_shipping_amount_map[$channel_uni_key]) ? $channel_shipping_amount_map[$channel_uni_key] : 0;
                if ($cur_shipping_rule) {
                    // 按订单金额配置
                    if ($cur_shipping_rule['type'] == 1) {
                        // 按订单金额阶梯配置
                        $shipping_amount = PriceAssistantService::getShareFee($total_order_amount, $numerator_amount,
                            $total_shipping_fee);
                        // 按订单金额阶梯配置
                    } elseif ($cur_shipping_rule['type'] == 2) {
                        $shipping_amount = PriceAssistantService::getShareFee($total_order_amount, $numerator_amount,
                            $total_shipping_fee);
                        // 按订单型号数量配置
                    } elseif ($cur_shipping_rule['type'] == 3) {
                        $shipping_amount = PriceAssistantService::getShareFee($order_total_number,
                            $goods_item['goods_number'], $total_shipping_fee);
                        // 按订单型号重量配置
                    } elseif ($cur_shipping_rule['type'] == 4) {
                        $numerator_weight = bcmul($goods_item['weight_kg'], $goods_item['goods_number'], 6);
                        $shipping_amount = PriceAssistantService::getShareFee($order_total_weight_kg, $numerator_weight,
                            $total_shipping_fee);
                    }
                }

                // 获取渠道其他费用，打卷费+包装费
                $other_fee = PriceAssistantService::getChannelOtherFee($other_fee_rule_list_map, $goods_item,
                    $delivery_place);
                $supply_channel_fee = bcadd($shipping_amount, $other_fee, 2);
                $supply_channel_fee_info = [
                    'supply_channel_fee' => $supply_channel_fee,
                    'supply_channel_fee_detail' => "总渠道费用：{$supply_channel_fee}, 运费：{$shipping_amount}, 其他费用：{$other_fee}",
                ];
            } else {
                $supply_channel_fee_info = [
                    'supply_channel_fee' => "0",
                    'supply_channel_fee_detail' => "不计算预计渠道费",
                ];
            }


            $goods_list_with_extra_charge[] = [
                'unique_key' => $goods_item['unique_key'],
                'goods_name' => $goods_item['goods_name'],
                'tariff_fee' => $tariff_fee_info['tariff_fee'],
                'tariff_fee_detail' => $tariff_fee_info['tariff_fee_detail'],
                'land_tax_fee' => $land_fee_info['land_tax_fee'],
                'land_tax_fee_detail' => $land_fee_info['land_tax_fee_detail'],
                'equivalent_tax_fee' => $equivalent_fee_info['equivalent_tax_fee'],
                'equivalent_tax_fee_detail' => $equivalent_fee_info['equivalent_tax_fee_detail'],
                'import_inspection_fee' => $import_inspection_fee_info['import_inspection_fee'],
                'import_inspection_fee_detail' => $import_inspection_fee_info['import_inspection_fee_detail'],
                'hk_clearance_fee' => $hk_clearance_fee_info['hk_clearance_fee'],
                'hk_clearance_fee_detail' => $hk_clearance_fee_info['hk_clearance_fee_detail'],
                'supply_channel_fee' => $supply_channel_fee_info['supply_channel_fee'],  //计算渠道费； 渠道运费+渠道其他费用
                'supply_channel_fee_detail' => $supply_channel_fee_info['supply_channel_fee_detail'],
            ];
        }
        return $goods_list_with_extra_charge;
    }


    // 获取商品关税信息
    public static function getGoodsTariffFeeInfo(
        $goods_info,
        $goods_tax_info,
        $delivery_place = self::DELIVERY_PALCE_MAINLAND
    ) {
        $tariff_fee_info = [
            'tariff_fee' => "0",
            'tariff_fee_detail' => "",
        ];

        // 如果没有查询到关税信息
        if (empty($goods_tax_info)) {
            $tariff_fee_info = [
                'tariff_fee' => "0",
                'tariff_fee_detail' => "未查询到商品关税信息",
            ];
            return $tariff_fee_info;
        }

        // 如果是大陆交货，计算关税
        if ($delivery_place == self::DELIVERY_PALCE_MAINLAND) {
            if (isset($goods_tax_info['tax_rate_low'])) {
                if ($goods_tax_info['tax_rate_low']) {
                    $goods_amount = bcmul($goods_info['goods_number'], $goods_info['goods_price'], 6);
                    $tax_rate_low = intval($goods_tax_info['tax_rate_low']);
                    $tariff_fee_info['tariff_fee'] = bcmul($goods_amount, bcdiv($tax_rate_low, 100, 2), 2);
                    $tariff_fee_info['tariff_fee_detail'] = "说明：该型号海关归类编码为：{$goods_tax_info['number']}，进口关税率：{$tax_rate_low}%，按价格和数量，得出：{$goods_info['goods_price']}X{$goods_info['goods_number']}X{$tax_rate_low}%={$tariff_fee_info['tariff_fee']} RMB";
                } else {
                    $tariff_fee_info['tariff_fee_detail'] = '商品关税为0';
                }
            } else {
                $tariff_fee_info['tariff_fee_detail'] = '商品未归类，关税信息查询为空';
            }
        } elseif ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $tariff_fee_info['tariff_fee_detail'] = '香港自由贸易港无关税';
        }
        return $tariff_fee_info;
    }

    // 获取商品产地税信息
    public static function getGoodsLandFeeInfo(
        $goods_info,
        $goods_tax_info,
        $delivery_place = self::DELIVERY_PALCE_MAINLAND
    ) {
        $land_fee_info = [
            'land_tax_fee' => "0",
            'land_tax_fee_detail' => "",
        ];

        // 如果没有查询到关税信息
        if (empty($goods_tax_info)) {
            $land_fee_info = [
                'land_tax_fee' => "0",
                'land_tax_fee_detail' => "未查询到商品关税信息",
            ];
            return $land_fee_info;
        }

        // 如果是大陆交货，计算关税
        if ($delivery_place == self::DELIVERY_PALCE_MAINLAND) {
            if (isset($goods_tax_info['tax_rate_land'])) {
                if ($goods_tax_info['tax_rate_land']) {
                    $goods_amount = bcmul($goods_info['goods_number'], $goods_info['goods_price'], 6);
                    $tax_rate_land = intval($goods_tax_info['tax_rate_land']);
                    $land_fee_info['land_tax_fee'] = bcmul($goods_amount, bcdiv($tax_rate_land, 100, 2), 2);
                    $land_fee_info['land_tax_fee_detail'] = "说明：该型号海关归类编码为：{$goods_tax_info['number']}，美国产地税率：{$tax_rate_land}%，按价格和数量，得出：{$goods_info['goods_price']}X{$goods_info['goods_number']}X{$tax_rate_land}%={$land_fee_info['land_tax_fee']} RMB";
                } else {
                    $land_fee_info['land_tax_fee_detail'] = '商品产地税为0';
                }
            } else {
                $land_fee_info['land_tax_fee_detail'] = '商品未归类，产地税信息查询为空';
            }
        } elseif ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $land_fee_info['land_tax_fee_detail'] = '香港自由贸易港无美国产地税';
        }
        return $land_fee_info;
    }

    // 获取商品对等关税信息
    public static function getGoodsEquivalentFeeInfo(
        $goods_info,
        $goods_tax_info,
        $delivery_place = self::DELIVERY_PALCE_MAINLAND
    ) {
        $equivalent_fee_info = [
            'equivalent_tax_fee' => "0",
            'equivalent_tax_fee_detail' => "",
        ];

        // 如果没有查询到关税信息
        if (empty($goods_tax_info)) {
            $equivalent_fee_info = [
                'equivalent_tax_fee' => "0",
                'equivalent_tax_fee_detail' => "未查询到商品关税信息",
            ];
            return $equivalent_fee_info;
        }

        // 如果是大陆交货，计算关税
        if ($delivery_place == self::DELIVERY_PALCE_MAINLAND) {
            if (isset($goods_tax_info['tax_rate_reciprocal'])) {
                if ($goods_tax_info['tax_rate_reciprocal']) {
                    $goods_amount = bcmul($goods_info['goods_number'], $goods_info['goods_price'], 6);
                    $tax_rate_reciprocal = intval($goods_tax_info['tax_rate_reciprocal']);
                    $equivalent_fee_info['equivalent_tax_fee'] = bcmul($goods_amount,
                        bcdiv($tax_rate_reciprocal, 100, 2), 2);
                    $equivalent_fee_info['equivalent_tax_fee_detail'] = "说明：该型号海关归类编码为：{$goods_tax_info['number']}，对等关税税率：{$tax_rate_reciprocal}%，按价格和数量，得出：{$goods_info['goods_price']}X{$goods_info['goods_number']}X{$tax_rate_reciprocal}%={$equivalent_fee_info['equivalent_tax_fee']} RMB";
                } else {
                    $equivalent_fee_info['equivalent_tax_fee_detail'] = '商品对等关税为0';
                }
            } else {
                $equivalent_fee_info['equivalent_tax_fee_detail'] = '商品未归类，对等关税信息查询为空';
            }
        } elseif ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $equivalent_fee_info['equivalent_tax_fee_detail'] = '香港自由贸易港无美国对等关税';
        }
        return $equivalent_fee_info;
    }


    // 获取商品型号对应海关编码次数
    // 因为预计商检费（整个订单，型号对应归类的海关编码，单个海关编码为一条计算，再再同海关编码的下面均摊）
    // 所以如果是相同海关编码，后续还需要做均分
    public static function getHgMemberCountMap($goods_list, $goods_tax_info_map)
    {
        $hg_member_count_map = [];
        foreach ($goods_list as $goods_item) {
            $goods_tax_info = isset($goods_tax_info_map[$goods_item['goods_name']]) ? $goods_tax_info_map[$goods_item['goods_name']] : [];
            if (isset($goods_tax_info['number'])) {
                if (!isset($hg_member_count_map[$goods_tax_info['number']])) {
                    $hg_member_count_map[$goods_tax_info['number']] = 0;
                }
                $hg_member_count_map[$goods_tax_info['number']]++;
            }
        }
        return $hg_member_count_map;
    }

    // 获取进口商检费用
    public static function getGoodsImportInspectionFeeInfo(
        $goods_tax_info,
        $delivery_place = self::DELIVERY_PALCE_MAINLAND,
        $hg_member_count_map = []
    ) {
        $import_inspection_fee_info = [
            'import_inspection_fee' => "0",
            'import_inspection_fee_detail' => "",
        ];
        // 如果没有查询到关税信息
        if (empty($goods_tax_info)) {
            $import_inspection_fee_info = [
                'import_inspection_fee' => "0",
                'import_inspection_fee_detail' => "未查询到商品关税信息",
            ];
            return $import_inspection_fee_info;
        }

        // 如果是大陆交货，计算关税
        if ($delivery_place == self::DELIVERY_PALCE_MAINLAND) {
            if (isset($goods_tax_info['supervision_con'])) {
                if ($goods_tax_info['supervision_con']) {
                    $hg_member_count = isset($hg_member_count_map[$goods_tax_info['number']]) ? $hg_member_count_map[$goods_tax_info['number']] : 1;
                    $share_rate = bcdiv(1, $hg_member_count, 2);
                    $import_inspection_fee_info['import_inspection_fee'] = bcmul("106.00", $share_rate, 2);
                    $import_inspection_fee_info['import_inspection_fee_detail'] = "该型号商品属于商检商品，海关编码：{$goods_tax_info['number']}, 海关编码商品数：{$hg_member_count} ,均分进口商检费用为{$import_inspection_fee_info['import_inspection_fee']} RMB";
                } else {
                    $import_inspection_fee_info['import_inspection_fee_detail'] = '商检费用为0';
                }
            } else {
                $import_inspection_fee_info['import_inspection_fee_detail'] = '商品未归类，进口商检信息查询为空';
            }
        } elseif ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $import_inspection_fee_info['import_inspection_fee_detail'] = '香港自由贸易港无进口商检费用';
        }
        return $import_inspection_fee_info;
    }

    // 获取订单总金额
    public static function getTotalOrderAmount($goods_list)
    {
        $total_order_amount = 0;
        foreach ($goods_list as $goods_item) {
            $goods_amount = bcmul($goods_item['goods_number'], $goods_item['goods_price'], 6);
            $total_order_amount = bcadd($total_order_amount, $goods_amount, 2);
        }
        return $total_order_amount;
    }

    // 获取订单总数量
    public static function getTotalOrderNumber($goods_list)
    {
        return array_sum(array_column($goods_list, 'goods_number'));
    }

    // 获取订单总重量
    public static function getTotalOrderWeightKg($goods_list)
    {
        $weight_kg = 0;
        foreach ($goods_list as $goods_item) {
            $i_weight_kg = bcmul($goods_item['weight_kg'], $goods_item['goods_number'], 6);
            $weight_kg = bcadd($weight_kg, $i_weight_kg, 2);
        }
        return $weight_kg;
    }

    // 获取总清关费
    public static function getTotalClearanceFee($goods_list, $delivery_place = self::DELIVERY_PALCE_MAINLAND)
    {
        $total_order_amount = 0;
        foreach ($goods_list as $goods_item) {
            $goods_amount = bcmul($goods_item['goods_number'], $goods_item['goods_price'], 6);
            $total_order_amount = bcadd($total_order_amount, $goods_amount, 2);
        }

        $hk_total_order_amount = 0;
        $hk_exchange_rate = PriceService::getExchangeRate(PriceService::CURRENCY_TYPE_HK);
        // 如果是大陆交货，那么转换成港币之后，计算清关费；如果是香港交货，那么就是美元，用美元转换为港币，在计算清关费
        if ($delivery_place == self::DELIVERY_PALCE_MAINLAND) {
            $hk_total_order_amount = PriceService::getBaseCurrencyPrice($total_order_amount, $hk_exchange_rate);
        } elseif ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $rmb_total_order_amount = PriceService::getRmbPrice($total_order_amount, PriceService::CURRENCY_TYPE_US);
            $hk_total_order_amount = PriceService::getPriceByExchangeRate($rmb_total_order_amount, $hk_exchange_rate);
        }

        //计算香港总清关费
        if ($hk_total_order_amount > 130000) {
            $hk_clearance_fee = bcmul(bcdiv($hk_total_order_amount, 1000, 6), 0.25, 2) + 50;
        } else {
            $hk_clearance_fee = 50;
        }
        // 计算费用，如果是大陆交货，要把港币转换成人民币；如果是香港交货，要把港币转换成美元。
        $total_clearance_fee = PriceService::getBaseCurrencyPrice($hk_clearance_fee, $hk_exchange_rate);
        if ($delivery_place == self::DELIVERY_PALCE_HONGKONG) {
            $us_exchange_rate = PriceService::getExchangeRate(PriceService::CURRENCY_TYPE_US);
            $total_clearance_fee = PriceService::getPriceByExchangeRate($total_clearance_fee, $us_exchange_rate);
        }
        return self::ceilToTwoDecimal($total_clearance_fee);
    }

    // 保留两位小数
    public static function ceilToTwoDecimal($value)
    {
        if ($value > 0) {
            return ceil($value * 100) / 100;
        }
        return 0;
    }


    // 计算按比例分摊的费用
    public static function getShareFee($denominator_amount, $numerator_amount, $total_fee)
    {
        // 防御性检查：非数字直接返回 0.00
        if (!is_numeric($denominator_amount) || !is_numeric($numerator_amount) || !is_numeric($total_fee)) {
            return '0.00';
        }

        // 如果分母或总费用为 0，结果肯定是 0.00
        if (
            bccomp($denominator_amount, '0', 6) === 0 ||
            bccomp($total_fee, '0', 2) === 0 ||
            bccomp($numerator_amount, '0', 6) === 0
        ) {
            return '0.00';
        }

        // 比例 = 分子 ÷ 分母
        $ratio = bcdiv($numerator_amount, $denominator_amount, 6);

        // 分摊费用 = 总费用 × 比例
        $shared_fee = bcmul($total_fee, $ratio, 2);

        return $shared_fee;
    }

    // 获取渠道运费配置
    public static function getChannelShippingRuleMap($goods_list)
    {
        $channel_ids = array_column($goods_list, 'channel_id');
        $shipping_rule_list = ShippingRuleModel::getShoppingRuleListByChannelIds($channel_ids);
        $shipping_rule_list_map = [];
        foreach ($shipping_rule_list as $shipping_rule) {
            if (empty($shipping_rule['rule'])) {
                continue;
            }
            $unique_channel_key = $shipping_rule['supplier_id'];
            if ($shipping_rule['supplier_id'] == self::CHANNEL_LIEXIN_ID) {
                $unique_channel_key = "{$shipping_rule['supplier_id']}_{$shipping_rule['supplier_code']}";
            }
            $shipping_rule_list_map[$unique_channel_key] = $shipping_rule;
        }
        return $shipping_rule_list_map;
    }

    // 获取渠道运费
    public static function getChannelShippingAmountMap($goods_list, $delivery_place = self::DELIVERY_PALCE_MAINLAND)
    {
        $shipping_rule_list_map = self::getChannelShippingRuleMap($goods_list);
        $channel_goods_list_map = [];
        foreach ($goods_list as $goods_item) {
            $channel_key = $goods_item['channel_id'];
            if ($goods_item['channel_id'] == self::CHANNEL_LIEXIN_ID) {
                $channel_key = "{$goods_item['channel_id']}_{$goods_item['supplier_code']}";
            }
            if (!isset($channel_goods_list_map[$channel_key])) {
                $channel_goods_list_map[$channel_key] = [];
            }
            $channel_goods_list_map[$channel_key][] = $goods_item;
        }

        $channel_shipping_amount_map = [];
        // 获取渠道商品列表map，渠道->商品列表，计算运费
        $us_exchange_rate = PriceService::getExchangeRate(PriceService::CURRENCY_TYPE_US);
        foreach ($channel_goods_list_map as $channel_uni_key => $goods_list) {
            $cur_shipping_rule = isset($shipping_rule_list_map[$channel_uni_key]) ? $shipping_rule_list_map[$channel_uni_key] : [];
            if ($cur_shipping_rule) {
                $ruleData = json_decode($cur_shipping_rule['rule'], true);
                // 按订单金额配置
                if ($cur_shipping_rule['type'] == 1) {
                    $order_total_amount = self::getTotalOrderAmount($goods_list);
                    $channel_shipping_amount_map[$channel_uni_key] = ChannelShippingPriceService::getShippingAmountByTotalAmount($order_total_amount,
                        $delivery_place, $us_exchange_rate, $ruleData);
                    // 按订单金额阶梯配置
                } elseif ($cur_shipping_rule['type'] == 2) {
                    $order_total_amount = self::getTotalOrderAmount($goods_list);
                    $channel_shipping_amount_map[$channel_uni_key] = ChannelShippingPriceService::getShippingAmountByLadderTotalAmount($order_total_amount,
                        $delivery_place, $us_exchange_rate, $ruleData);
                    // 按订单型号数量配置
                } elseif ($cur_shipping_rule['type'] == 3) {
                    $order_total_number = self::getTotalOrderNumber($goods_list);
                    $channel_shipping_amount_map[$channel_uni_key] = ChannelShippingPriceService::getShippingAmountByTotalNumber($order_total_number,
                        $delivery_place, $us_exchange_rate, $ruleData);
                    // 按订单型号重量配置
                } elseif ($cur_shipping_rule['type'] == 4) {
                    $order_total_weight_kg = self::getTotalOrderWeightKg($goods_list);
                    $channel_shipping_amount_map[$channel_uni_key] = ChannelShippingPriceService::getShippingAmountByTotalWeightKg($order_total_weight_kg,
                        $delivery_place, $us_exchange_rate, $ruleData);
                }
            }
        }
        return $channel_shipping_amount_map;
    }

    // 获取渠道其他费用配置
    public static function getChannelOtherFeeRuleMap($goods_list)
    {
        $channel_ids = array_column($goods_list, 'channel_id');
        $other_rule_list = OtherFeeRuleModel::getOtherFeeListByChannelIds($channel_ids);
        $other_rule_list_map = [];
        foreach ($other_rule_list as $other_rule) {
            if (empty($other_rule['rolling_rule']) && empty($other_rule['operation_rule'])) {
                continue;
            }
            $unique_channel_key = $other_rule['supplier_id'];
            if ($other_rule['supplier_id'] == self::CHANNEL_LIEXIN_ID) {
                $unique_channel_key = "{$other_rule['supplier_id']}_{$other_rule['supplier_code']}";
            }
            $other_rule_list_map[$unique_channel_key] = $other_rule;
        }
        return $other_rule_list_map;
    }


    // 获取渠道其他费用
    public static function getChannelOtherFee(
        $other_rule_list_map,
        $goods_item,
        $delivery_place = self::DELIVERY_PALCE_MAINLAND
    ) {
        $channel_uni_key = $goods_item['channel_id'];
        if ($goods_item['channel_id'] == PriceAssistantService::CHANNEL_LIEXIN_ID) {
            $channel_uni_key = "{$goods_item['channel_id']}_{$goods_item['supplier_code']}";
        }
        $other_fee = 0;
        $other_fee_rule = isset($other_rule_list_map[$channel_uni_key]) ? $other_rule_list_map[$channel_uni_key] : [];
        if ($other_fee_rule) {
            $rolling_fee = 0;
            $operation_fee = 0;

            // 打卷费
            $rollingRuleData = json_decode($other_fee_rule['rolling_rule'], true);
            if ($rollingRuleData && ($rollingRuleData['is_charge'] == 1)) {
                //应用全渠道商品
                if ($rollingRuleData['config_type'] == 1) {
                    if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                        $rolling_fee = $rollingRuleData['cny_fee'] ?? 0;
                    } else {
                        $rolling_fee = $rollingRuleData['usd_fee'] ?? 0;
                    }
                } else {
                    //按包装方式匹配
                    $package_keywords = explode(',', $rollingRuleData['package_keyword']);
                    if (in_array($goods_item['brand_pack'], $package_keywords)) {
                        if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                            $rolling_fee = $rollingRuleData['cny_fee'] ?? 0;
                        } else {
                            $rolling_fee = $rollingRuleData['usd_fee'] ?? 0;
                        }
                    }
                }
            }

            // 包装费
            $operationRuleData = json_decode($other_fee_rule['operation_rule'], true);
            if ($operationRuleData && ($operationRuleData['is_charge'] == 1)) {
                //应用全渠道商品
                if ($operationRuleData['config_type'] == 1) {
                    if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                        $operation_fee = $operationRuleData['cny_fee'] ?? 0;
                    } else {
                        $operation_fee = $operationRuleData['usd_fee'] ?? 0;
                    }
                } else {
                    //按包装方式匹配
                    $package_keywords = explode(',', $operationRuleData['package_keyword']);
                    if (in_array($goods_item['brand_pack'], $package_keywords)) {
                        if ($delivery_place == PriceAssistantService::DELIVERY_PALCE_MAINLAND) {
                            $operation_fee = $operationRuleData['cny_fee'] ?? 0;
                        } else {
                            $operation_fee = $operationRuleData['usd_fee'] ?? 0;
                        }
                    }
                }
            }

            $other_fee = bcadd($rolling_fee, $operation_fee, 2);
        }

        return $other_fee;
    }


}
