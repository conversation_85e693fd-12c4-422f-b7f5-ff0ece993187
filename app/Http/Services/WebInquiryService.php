<?php

namespace App\Http\Services;

use App\Http\Models\Crm\UserModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\WebInquiryConfirmedQuoteModel;
use App\Http\Models\WebInquiryItemsModel;
use App\Http\Utils\NameConvert;
use Illuminate\Support\Facades\Log;

class WebInquiryService
{
    // 获取客户业务员
    public static function getCustomerBindSales($user_id)
    {
        $sales_info = [
            'sale_id' => 0,
            'sale_name' => '',
            'user_sn' => '',
        ];
        $crm_user_info = UserModel::getUserInfoByUserIdAndOrgId($user_id);
        if ($crm_user_info) {
            $sales_info = [
                'sale_id' => $crm_user_info['sale_id'],
                'sale_name' => $crm_user_info['sale_name'],
                'user_sn' => $crm_user_info['user_sn'],
            ];
        }
        return $sales_info;
    }

    public static function getFormatWebInquiryItemInfo($web_inquiry_item_info)
    {
        $format_web_inquiry_item = [];
        if ($web_inquiry_item_info) {
            $format_web_inquiry_item = [
                'web_inquiry_item_id' => $web_inquiry_item_info['id'],
                'goods_name' => ($web_inquiry_item_info['verify_goods_name']) ? $web_inquiry_item_info['verify_goods_name'] : $web_inquiry_item_info['goods_name'],
                'brand_name' => ($web_inquiry_item_info['verify_brand_name']) ? $web_inquiry_item_info['verify_brand_name'] : $web_inquiry_item_info['brand_name'],
                'inquiry_number' => ($web_inquiry_item_info['verify_inquiry_number']) ? $web_inquiry_item_info['verify_inquiry_number'] : $web_inquiry_item_info['inquiry_number'],
                'web_show_price' => $web_inquiry_item_info['web_show_price'],
                'currency' => $web_inquiry_item_info['currency'],
                'currency_val' => NameConvert::getCurrencyName($web_inquiry_item_info['currency']),
                'delivery_time' => ($web_inquiry_item_info['verify_delivery_time']) ? $web_inquiry_item_info['verify_delivery_time'] : $web_inquiry_item_info['delivery_time'],
                'batch' => ($web_inquiry_item_info['verify_batch']) ? $web_inquiry_item_info['verify_batch'] : $web_inquiry_item_info['batch'],
                'remark' => ($web_inquiry_item_info['verify_remark']) ? $web_inquiry_item_info['verify_remark'] : $web_inquiry_item_info['special_request'],
                'status_val' => "",
            ];
        }
        return $format_web_inquiry_item;
    }

    public static function getFormatBuyerQuote($buyer_quote_info)
    {
        $format_buyer_quote_info = [];
        if ($buyer_quote_info) {
            $format_buyer_quote_info = [
                'quote_id' => $buyer_quote_info['id'],
                'goods_name' => $buyer_quote_info['goods_name'],
                'brand_name' => $buyer_quote_info['brand_name'],
                'quote_number' => $buyer_quote_info['quote_number'],
                'quote_price' => ($buyer_quote_info['currency'] == PriceService::CURRENCY_TYPE_RMB) ? $buyer_quote_info['price_rmb'] : $buyer_quote_info['price_origin'],
                'currency' => $buyer_quote_info['currency'],
                'currency_val' => NameConvert::getCurrencyName($buyer_quote_info['currency']),
                'delivery_time' => $buyer_quote_info['delivery_time'],
                'batch' => $buyer_quote_info['batch'],
                'remark' => $buyer_quote_info['remark'],
                'expire_time' => NameConvert::getDateTime($buyer_quote_info['expire_time']),
                'status_val' => "已报价",
            ];
        }
        return $format_buyer_quote_info;
    }

    public static function getFormatSaleConfirmedQuote($sale_confirmed_quote)
    {
        $format_sale_confirmed_quote = [];
        if ($sale_confirmed_quote) {
            $format_sale_confirmed_quote = [
                'goods_name' => $sale_confirmed_quote['goods_name'],
                'brand_name' => $sale_confirmed_quote['brand_name'],
                'quote_number' => $sale_confirmed_quote['quote_number'],
                'quote_price' => $sale_confirmed_quote['quote_price'],
                'currency' => $sale_confirmed_quote['currency'],
                'currency_val' => NameConvert::getCurrencyName($sale_confirmed_quote['currency']),
                'delivery_time' => $sale_confirmed_quote['delivery_time'],
                'delivery_info' => NameConvert::getDeliveryInfo($sale_confirmed_quote['delivery_time']),
                'batch' => $sale_confirmed_quote['batch'],
                'remark' => $sale_confirmed_quote['remark'],
                'expire_time' => NameConvert::getDateTime($sale_confirmed_quote['expire_time']),
                'status_val' => NameConvert::getConfiremedStatusName($sale_confirmed_quote['confirmed_status']),
            ];
        }
        return $format_sale_confirmed_quote;
    }

    public static function getFormatDefaultSaleConfirmedQuote($buyer_quote_info)
    {
        $format_sale_confirmed_quote = [];
        if ($buyer_quote_info) {
            $quote_price = ($buyer_quote_info['currency'] == PriceService::CURRENCY_TYPE_RMB) ? $buyer_quote_info['price_rmb'] : $buyer_quote_info['price_origin'];
            if ($buyer_quote_info['selling_price']) {
                $quote_price = $buyer_quote_info['selling_price'];
            }
            $format_sale_confirmed_quote = [
                'goods_name' => $buyer_quote_info['goods_name'],
                'brand_name' => $buyer_quote_info['brand_name'],
                'quote_number' => $buyer_quote_info['quote_number'],
                'quote_price' => $quote_price,
                'currency' => $buyer_quote_info['currency'],
                'currency_val' => NameConvert::getCurrencyName($buyer_quote_info['currency']),
                'delivery_time' => $buyer_quote_info['delivery_time'],
                'delivery_info' => NameConvert::getDeliveryInfo($buyer_quote_info['delivery_time']),
                'batch' => $buyer_quote_info['batch'],
                'remark' => $buyer_quote_info['remark'],
                'expire_time' => NameConvert::getDateTime($buyer_quote_info['expire_time']),
                'status_val' => "",
            ];
        }
        return $format_sale_confirmed_quote;
    }

    public static function getWebInquiryAggrCount($web_inquiry_id)
    {
        $aggr_count_map = WebInquiryItemsModel::getStatusCountsByInquiryIds([$web_inquiry_id]);
        return isset($aggr_count_map[$web_inquiry_id]) ? $aggr_count_map[$web_inquiry_id] : [];
    }

    public static function getWebInquiryAggrCountMapByWebInquiryIds($web_inquiry_ids)
    {
        return WebInquiryItemsModel::getStatusCountsByInquiryIds($web_inquiry_ids);
    }

    // 获取网站询价明细已报价数
    public static function getWebInquiryQuotedCount($web_inquiry_id)
    {
        $quote_count = 0;
        $web_inquiry_items = WebInquiryItemsModel::getListByWebInquiryId($web_inquiry_id);
        $rela_inquiry_item_ids = array_column($web_inquiry_items, 'rela_inquiry_item_id');
        $rela_inquiry_item_ids = array_values(array_filter($rela_inquiry_item_ids));
        if ($rela_inquiry_item_ids) {
            $quote_count = InquiryItemModel::getQuotedCountByInquiryItemIds($rela_inquiry_item_ids);
        }
        return $quote_count;
    }

    // 获取网站询价明细已确认报价数
    public static function getWebInquiryPuchedCount($web_inquiry_id)
    {
        return WebInquiryConfirmedQuoteModel::getPushedCountByWebInquiryId($web_inquiry_id);
    }

    // 自动确认报价
    public static function autoConfirmedWebInquiryItem($inquiry_item_id, $quote_id)
    {
        $inquiry_item_info = InquiryItemModel::getById($inquiry_item_id);
        $quote_info = QuoteModel::getById($quote_id);
        $web_inquiry_item_info = WebInquiryItemsModel::getWebInquiryItemById($inquiry_item_info['web_inquiry_item_id']);
        $confirmed_info = WebInquiryConfirmedQuoteModel::getConfirmInfoByWebInquiryItemId($web_inquiry_item_info['id']);
        if (empty($confirmed_info)) {
            $add_confirm_info = [
                'web_inquiry_id' => $web_inquiry_item_info['web_inquiry_id'],
                'web_inquiry_items_id' => $web_inquiry_item_info['id'],
                'goods_name' => $web_inquiry_item_info['goods_name'],
                'brand_id' => $quote_info['brand_id'],
                'brand_name' => $quote_info['brand_name'],
                'quote_price' => $quote_info['selling_price'],
                'quote_number' => $inquiry_item_info['inquiry_number'],
                'currency' => $quote_info['currency'],
                'delivery_time' => $quote_info['delivery_time'],
                'batch' => $quote_info['batch'],
                'remark' => "",
                'quote_id' => $quote_id,
                'expire_time' => $quote_info['expire_time'],
                'confirmed_status' => 1,
                'create_name' => $quote_info['create_name'],
                'create_time' => time(),
            ];
            WebInquiryConfirmedQuoteModel::createConfirmItem($add_confirm_info);
            WebInquiryItemsModel::updateById([
                'item_status' => WebInquiryItemsModel::STATUS_CONFIRMED
            ], $web_inquiry_item_info['id']);
            //更新平台网站询价状态
            BomMatchService::updateLiexinInquiryItemQuoteStatus($web_inquiry_item_info['source_item_id']);
        } else {
            Log::info("web_inquiry_item_id:{$inquiry_item_info['web_inquiry_item_id']}已有确认报价信息，无需在自动确认报价");
        }

        return true;
    }

}
