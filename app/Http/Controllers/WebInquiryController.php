<?php

namespace App\Http\Controllers;

use App\Http\ApiHelper\ApiCode;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\Crm\UserModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\InquiryModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\QuoteSelectedModel;
use App\Http\Models\WebInquiryConfirmedQuoteModel;
use App\Http\Models\WebInquiryItemsModel;
use App\Http\Models\WebInquiryModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\BomMatchService;
use App\Http\Services\CustomerService;
use App\Http\Services\InquiryService;
use App\Http\Services\PermService;
use App\Http\Services\WebInquiryService;
use App\Http\Strategy\UserSysMsgStrategy\QuoteSelected;
use App\Http\Utils\ErrMsg;
use App\Http\Utils\NameConvert;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use League\CommonMark\Extension\SmartPunct\Quote;

class WebInquiryController extends Controller
{

    //网站询价列表
    public function webInquiryBoard(Request $request)
    {
        return view('webInquiry.webInquiryBoard');
    }

    //网站询价详情
    public function webInquiryDetail(Request $request)
    {
        return view('webInquiry.webInquiryDetail');
    }

    // 添加网站询价单
    public function addWebInquiry(Request $request)
    {
        try {
            DB::connection("rfq")->beginTransaction();
            // 检验请求参数
            $add_web_inquiry_params = $this->getAddWebInquiryParams($request);
            $web_inquiry_items = $request->input("web_inquiry_items");
            if (empty($web_inquiry_items)) {
                return $this->setError("请传递网站询价单明细信息");
            }
            $validator = Validator::make(['web_inquiry_items' => $web_inquiry_items], [
                'web_inquiry_items' => 'required|array',
                'web_inquiry_items.*.source_item_id' => 'required',
                'web_inquiry_items.*.goods_name' => 'required',
                'web_inquiry_items.*.brand_name' => 'required',
                'web_inquiry_items.*.inquiry_number' => 'required',
                'web_inquiry_items.*.web_show_price' => 'present',
                'web_inquiry_items.*.expect_price' => 'present',
                'web_inquiry_items.*.delivery_time' => 'present',
                'web_inquiry_items.*.batch' => 'present',
                'web_inquiry_items.*.special_request' => 'present',
                'web_inquiry_items.*.sku_id' => 'present',
            ]);
            if ($validator->fails()) {
                $errors = $validator->errors()->toArray();
                $err_msg = ValidatorMsg::getMsg($errors);
                return $this->setError($err_msg);
            }

            $bind_sele_info = WebInquiryService::getCustomerBindSales($add_web_inquiry_params['user_id']);
            $add_web_inquiry_info = [
                'web_inquiry_sn' => $add_web_inquiry_params['web_inquiry_sn'],
                'source_id' => $add_web_inquiry_params['source_id'],
                'customer_name' => $add_web_inquiry_params['customer_name'],
                'customer_account' => $add_web_inquiry_params['customer_account'],
                'delivery_place' => $add_web_inquiry_params['delivery_place'],
                'currency' => $add_web_inquiry_params['currency'],
                'user_id' => $add_web_inquiry_params['user_id'],
                'user_sn' => $bind_sele_info['user_sn'],
                'create_uid' => $bind_sele_info['sale_id'],
                'create_name' => $bind_sele_info['sale_name'],
                'create_time' => time(),
            ];

            // 判断网站询价单是否已创建，如果已创建，那么不在创建，直接返回创建成功
            $web_inquiry_info = WebInquiryModel::getWebInquiryBySn($add_web_inquiry_info['web_inquiry_sn']);
            if ($web_inquiry_info) {
                $web_inquiry_items = WebInquiryItemsModel::getListByWebInquiryId($web_inquiry_info['web_inquiry_id']);
                $source_item_id_rela_webinquiry_item_id_map = array_column($web_inquiry_items, 'id', 'source_item_id');
                return $this->setSuccess([
                    'web_inquiry_id' => $web_inquiry_info['web_inquiry_id'],
                    'web_inquiry_sn' => $add_web_inquiry_params['web_inquiry_sn'],
                    'rela_web_inquiry_item_map' => $source_item_id_rela_webinquiry_item_id_map,
                ], ApiCode::API_CODE_SUCCESS, "网站询价单已创建");
            }

            // 创建网站询价单主表
            $web_inquiry_id = WebInquiryModel::createWebInquiry($add_web_inquiry_info);

            // 创建网站询价单明细
            $source_item_id_rela_webinquiry_item_id_map = [];
            $auto_generate_inquiry_ids = [];
            foreach ($web_inquiry_items as $web_inquiry_item) {
                $add_web_inquiry_item_info = [
                    'web_inquiry_id' => $web_inquiry_id,
                    'source_item_id' => $web_inquiry_item['source_item_id'],
                    'goods_name' => $web_inquiry_item['goods_name'],
                    'brand_name' => $web_inquiry_item['brand_name'],
                    'inquiry_number' => $web_inquiry_item['inquiry_number'],
                    'currency' => $add_web_inquiry_params['currency'],
                    'web_show_price' => (float)$web_inquiry_item['web_show_price'],
                    'expect_price' => (float)$web_inquiry_item['expect_price'],
                    'delivery_time' => (string)$web_inquiry_item['delivery_time'],
                    'batch' => (string)$web_inquiry_item['batch'],
                    'special_request' => (string)$web_inquiry_item['special_request'],
                    'sku_id' => (string)$web_inquiry_item['sku_id'],
                ];
                $web_inquiry_item_id = WebInquiryItemsModel::createWebInquiryItem($add_web_inquiry_item_info);
                $source_item_id_rela_webinquiry_item_id_map[$web_inquiry_item['source_item_id']] = $web_inquiry_item_id;

                // 如果有sku_id，那么自动生成询价单
                if ($web_inquiry_item['sku_id']) {
                    $auto_generate_inquiry_ids[] = $web_inquiry_item_id;
                }
            }
            DB::connection("rfq")->commit();

            // 如果有关联sku，那么自动生成询价单
            if ($auto_generate_inquiry_ids) {
                $RabbitQueueModel = new RabbitQueueModel();
                $RabbitQueueModel->insertQueue('/queue/webInquiry/generateInquiry', [
                    "web_inquiry_item_ids" => implode(',', $auto_generate_inquiry_ids),
                ], $RabbitQueueModel::QUEUE_FRQ);
            }

            return $this->setSuccess([
                'web_inquiry_id' => $web_inquiry_id,
                'web_inquiry_sn' => $add_web_inquiry_params['web_inquiry_sn'],
                'rela_web_inquiry_item_map' => $source_item_id_rela_webinquiry_item_id_map,
            ], ApiCode::API_CODE_SUCCESS, "添加成功");
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("rfq")->rollBack();
            return $this->setError("添加失败:" . $exception->getMessage());
        }

    }

    // 添加网站询价单主单参数
    private function getAddWebInquiryParams(Request $request)
    {
        // 检验请求参数
        $params = [
            'web_inquiry_sn' => $request->input("web_inquiry_sn"),
            'source_id' => $request->input("source_id"),
            'customer_name' => $request->input("customer_name"),
            'customer_account' => $request->input("customer_account"),
            'user_id' => $request->input("user_id"),
            'delivery_place' => $request->input("delivery_place"),
            'currency' => $request->input("currency"),
        ];

        $validator = Validator::make($params, [
            'source_id' => 'required|numeric',
            'web_inquiry_sn' => 'required',
            'customer_name' => 'required',
            'customer_account' => 'required',
            'user_id' => 'required',
            'delivery_place' => 'required',
            'currency' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            throw new \Exception($err_msg);
        }
        return $params;
    }

    public function getWebInquiryList(Request $request)
    {
        $params = [
            'page' => $request->input("page", 1),
            'limit' => $request->input("limit", 10),
            'create_time' => $request->input("create_time", ""),
            'goods_name' => $request->input("goods_name"),
            'status' => $request->input("status"),
            'web_inquiry_sn' => $request->input('web_inquiry_sn'),
            'customer_name' => $request->input('customer_name'),
            'create_name' => $request->input('create_name'),
        ];
        $create_time = $request->input("create_time");
        if ($create_time) {
            [$start, $end] = explode('~', $create_time);
            $params['start_create_time'] = $start;
            $params['end_create_time'] = $end;
        }
        $where = $this->buildSearchWhere($params);

        $extra_where = [];
        // 如果有查询商品名称，那么需要查明细表
        if ($params['goods_name']) {
            $extra_where['in_web_inquiry_ids'] = WebInquiryItemsModel::getRelaWebInquiryIdsByGoodsName($params['goods_name']);
            if (empty($extra_where['in_web_inquiry_ids'])) {
                return $this->setSuccess(['list' => [], 'total' => 0]);
            }
        }
        // 权限控制，默认只能查看自己的，有查看全部和查看下级的权限
        $extra_where['sale_uids'] = $this->getPermSaleIds();
        $web_inquiry_list = WebInquiryModel::getListByWhere($where, $params['page'], $params['limit'], $extra_where);

        if ($web_inquiry_list && $web_inquiry_list['data']) {
            $web_inquiry_ids = array_column($web_inquiry_list['data'], 'web_inquiry_id');
            $aggr_count_map = WebInquiryService::getWebInquiryAggrCountMapByWebInquiryIds($web_inquiry_ids);
            foreach ($web_inquiry_list['data'] as &$web_inquiry) {
                $web_inquiry['total_item_count'] = isset($aggr_count_map[$web_inquiry['web_inquiry_id']]) ? $aggr_count_map[$web_inquiry['web_inquiry_id']]['total_item_count'] : 0;
                $web_inquiry['unconfirmed_count'] = isset($aggr_count_map[$web_inquiry['web_inquiry_id']]) ? $aggr_count_map[$web_inquiry['web_inquiry_id']]['unconfirmed_count'] : 0;
                $web_inquiry['confirmed_count'] = isset($aggr_count_map[$web_inquiry['web_inquiry_id']]) ? $aggr_count_map[$web_inquiry['web_inquiry_id']]['confirmed_count'] : 0;
                $web_inquiry['ordered_count'] = isset($aggr_count_map[$web_inquiry['web_inquiry_id']]) ? $aggr_count_map[$web_inquiry['web_inquiry_id']]['ordered_count'] : 0;
                $web_inquiry['delivery_place_val'] = NameConvert::getDeliveryTypeName($web_inquiry['delivery_place']);
                $web_inquiry['create_time'] = NameConvert::getDateTime($web_inquiry['create_time']);
                $web_inquiry['update_time'] = NameConvert::getDateTime($web_inquiry['update_time']);
            }
        }
        $data = [
            "list" => $web_inquiry_list['data'],
            "total" => isset($web_inquiry_list['total']) ? $web_inquiry_list['total'] : 0,
        ];
        return $this->setSuccess($data);
    }

    // 获取销售用户ids，判断用户权限，默认只能查看自己的询价单，如果有查看下级，那么查询下级人的。
    private function getPermSaleIds()
    {
        $sale_uids = [request()->user->userId, 0];
        if (PermService::hasPerm("web_inquiry_list_all")) {
            $sale_uids = [];
        } else {
            if (PermService::hasPerm("web_inquiry_list_department")) {
                $sale_uids = PermService::getSubUserId(request()->user->userId);
                $sale_uids[] = 0;
            }
        }
        return $sale_uids;
    }

    private function buildSearchWhere($params, $is_only_7days = false)
    {
        $where = [];
        // 网站询价单号
        if (isset($params['web_inquiry_sn']) && $params['web_inquiry_sn']) {
            $where[] = ['web_inquiry_sn', '=', $params['web_inquiry_sn']];
        }
        // 查询询价客户
        if (isset($params['customer_name']) && $params['customer_name']) {
            $where[] = ['customer_name', 'like', $params['customer_name'] . '%'];
        }
        // 账号
        if (isset($params['customer_account']) && $params['customer_account']) {
            $where[] = ['customer_account', 'like', $params['customer_account'] . '%'];
        }
        // 查询销售员
        if (isset($params['create_name']) && $params['create_name']) {
            $where[] = ['create_name', '=', $params['create_name']];
        }
        // 创建时间
        if (isset($params['start_create_time']) && $params['start_create_time']) {
            $where[] = ['create_time', '>=', strtotime($params['start_create_time'])];
        }
        if (isset($params['end_create_time']) && $params['end_create_time']) {
            $where[] = ['create_time', '<=', strtotime($params['end_create_time']) + 86399];
        }
        return $where;
    }

    // 领取网站询价
    public function takeWebInquiry(Request $request)
    {
        $web_inquiry_id = $request->input("web_inquiry_id");
        $web_inquiry_info = WebInquiryModel::getWebInquiryById($web_inquiry_id);
        if (!$web_inquiry_info) {
            return $this->setError("网站询价信息查询失败");
        }
        // 如果网站询价单已有销售员，那么不能领取
        if ($web_inquiry_info['create_uid']) {
            return $this->setError("网站询价单已有销售员，不能领取.");
        }

        $take_info = [
            'create_uid' => $request->user->userId,
            'create_name' => $request->user->name,
        ];

        WebInquiryModel::updateByUserId($take_info, $web_inquiry_info['user_id']);

        // 队列到crm，绑定用户
        $RabbitQueueModel = new RabbitQueueModel();
        $queue_data = [
            'org_id' => 1,
            'sale_id' => $request->user->userId,
            'user_id' => $web_inquiry_info['user_id'],
        ];
        $RabbitQueueModel->insertQueue('/sync/user/updateUserSale', $queue_data, RabbitQueueModel::QUEUE_CRM);

        return $this->setSuccess("领取询价成功");
    }

    // 网站询价详情
    public function getWebInquiryDetail(Request $request)
    {
        $web_inquiry_id = $request->input("web_inquiry_id");
        $web_inquiry_info = WebInquiryModel::getWebInquiryById($web_inquiry_id);
        $web_inquiry_aggr_count = WebInquiryService::getWebInquiryAggrCount($web_inquiry_info['web_inquiry_id']);
        $web_inquiry_info['total_item_count'] = $web_inquiry_aggr_count['total_item_count'];
        $web_inquiry_info['unconfirmed_count'] = $web_inquiry_aggr_count['unconfirmed_count'];
        $web_inquiry_info['confirmed_count'] = $web_inquiry_aggr_count['confirmed_count'];
        $web_inquiry_info['ordered_count'] = $web_inquiry_aggr_count['ordered_count'];
        $web_inquiry_info['delivery_place_val'] = NameConvert::getDeliveryTypeName($web_inquiry_info['delivery_place']);
        $web_inquiry_info['create_time'] = NameConvert::getDateTime($web_inquiry_info['create_time']);
        $data = [
            'web_inquiry_info' => $web_inquiry_info
        ];
        return $this->setSuccess($data);
    }

    // 修改客户询价
    public function updateVerifyInquiryInfo(Request $request)
    {
        $web_inquiry_item_id = $request->input("web_inquiry_item_id");
        $web_inquiry_item_info = WebInquiryItemsModel::getWebInquiryItemById($web_inquiry_item_id);
        if (empty($web_inquiry_item_info)) {
            return $this->setError("网站询价单明细信息查询失败");
        }

        $params = [
            'goods_name' => $request->input("goods_name"),
            'brand_name' => $request->input("brand_name"),
            'inquiry_number' => $request->input("inquiry_number"),
            'batch' => $request->input("batch"),
            'delivery_time' => $request->input("delivery_time"),
            'deadline_time' => $request->input("deadline_time"),
            'remark' => $request->input("remark"),
        ];

        // 检测是否有标准品牌，否则不能添加
        $params['brand_name'] = trim($params['brand_name']);
        $standard_brand_id = InquiryService::getStandardBrandId($params['brand_name']);
        if (empty($standard_brand_id)) {
            return $this->setError("{$params['brand_name']} 标准品牌不存在,请联系运营同事（徐腾渊）添加");
        }

        $update_info = [
            'verify_goods_name' => (string)$params['goods_name'],
            'verify_brand_name' => (string)$params['brand_name'],
            'verify_brand_id' => $standard_brand_id,
            'verify_inquiry_number' => (int)$params['inquiry_number'],
            'verify_batch' => (string)$params['batch'],
            'verify_delivery_time' => (string)$params['delivery_time'],
            'verify_deadline_time' => strtotime($params['deadline_time']),
            'verify_remark' => (string)$params['remark'],
        ];

        WebInquiryItemsModel::updateById($update_info, $web_inquiry_item_id);
        return $this->setSuccess("修改客户询价成功");
    }

    // 生成询价单
    public function generateInquiry(Request $request)
    {
        $web_inquiry_item_ids = $request->input("web_inquiry_item_ids");
        $web_inquiry_item_ids = explode(",", $web_inquiry_item_ids);
        $web_inquiry_item_ids = array_values(array_unique(array_filter($web_inquiry_item_ids)));
        if (empty($web_inquiry_item_ids)) {
            return $this->setError("请传递网站询价明细ids");
        }

        $web_inquiry_item_list = WebInquiryItemsModel::getListByIds($web_inquiry_item_ids);
        $web_inquiry_ids = array_column($web_inquiry_item_list, 'web_inquiry_id');
        $web_inquiry_info = WebInquiryModel::getWebInquiryById($web_inquiry_ids[0]);
        if (empty($web_inquiry_info)) {
            return $this->setError("关联网站询价单查询失败");
        }
        if (empty($web_inquiry_info['create_name'])) {
            return $this->setError("网站询价单没有关联销售员，请先领取网站询价单");
        }

        foreach ($web_inquiry_item_list as $web_inquiry_item) {
            if ($web_inquiry_item['rela_inquiry_sn']) {
                return $this->setError("型号:{$web_inquiry_item['goods_name']},已关联询价单，不能重复生成");
            }
        }

        // 创建询价单
        $multi_inquiry_id_map = [];
        try {
            DB::connection("rfq")->beginTransaction();

            foreach ($web_inquiry_item_list as $web_inquiry_item) {
                $standard_brand_id = $web_inquiry_item['verify_brand_id'];
                if (empty($standard_brand_id)) {
                    // 检测是否有标准品牌，否则不能添加
                    $standard_brand_id = InquiryService::getStandardBrandId($web_inquiry_item['brand_name']);
                    if (empty($standard_brand_id)) {
                        return $this->setError("\"{$web_inquiry_item['brand_name']}\" 标准品牌不存在,请联系运营同事（徐腾渊）添加");
                    }
                }
                $inquiry_param = [
                    'goods_name' => $web_inquiry_item['verify_goods_name'] ? $web_inquiry_item['verify_goods_name'] : $web_inquiry_item['goods_name'],
                    'brand_name' => $web_inquiry_item['verify_brand_name'] ? $web_inquiry_item['verify_brand_name'] : $web_inquiry_item['brand_name'],
                    'inquiry_number' => $web_inquiry_item['verify_inquiry_number'] ? $web_inquiry_item['verify_inquiry_number'] : $web_inquiry_item['inquiry_number'],
                    'delivery_place' => $web_inquiry_info['delivery_place'],
                    'delivery_time' => $web_inquiry_item['verify_delivery_time'] ? $web_inquiry_item['verify_delivery_time'] : $web_inquiry_item['delivery_time'],
                    'batch' => $web_inquiry_item['verify_batch'] ? $web_inquiry_item['verify_batch'] : $web_inquiry_item['batch'],
                    'remark' => $web_inquiry_item['verify_remark'] ? $web_inquiry_item['verify_remark'] : $web_inquiry_item['special_request'],
                    'user_name' => $web_inquiry_info['customer_name'],
                    'deadline_time' => NameConvert::getDateTime($web_inquiry_item['verify_deadline_time']),
                    'inquiry_create_user_name' => $web_inquiry_info['create_name'],
                    'inquiry_create_user_id' => $web_inquiry_info['create_uid'],
                ];

                // 是否CRM维护客户
                $customer_name = trim($inquiry_param['user_name']);
                $format_customer_list = CustomerService::getCustomCompany($inquiry_param['inquiry_create_user_id'],
                    $customer_name);
                // 公司名称
                $crm_customer_name_info_map = array_column($format_customer_list, null, 'com_name');
                // 手机号
                $crm_customer_mobile_info_map = array_column($format_customer_list, null, 'mobile');
                // 邮箱
                $crm_customer_email_info_map = array_column($format_customer_list, null, 'email');

                $crm_customer_info_map = $crm_customer_name_info_map + $crm_customer_mobile_info_map + $crm_customer_email_info_map;
                if (array_key_exists($customer_name, $crm_customer_info_map)) {
                    $inquiry_param['user_types'] = $crm_customer_info_map[$customer_name]['com_id'] ? InquiryModel::USER_TYPE_COMPANY : InquiryModel::USER_TYPE_SINGLE;
                    $inquiry_param['user_id'] = $crm_customer_info_map[$customer_name]['user_id'] ?? 0;
                    $inquiry_param['com_id'] = $crm_customer_info_map[$customer_name]['com_id'] ?? 0;
                    $inquiry_param['com_name'] = $crm_customer_info_map[$customer_name]['com_name'] ?? 0;
                } else {
                    $inquiry_param['user_types'] = InquiryModel::USER_TYPE_SINGLE;
                    $inquiry_param['user_id'] = $web_inquiry_info['user_id'] ?? 0;
                    $inquiry_param['com_id'] = 0;
                    $inquiry_param['com_name'] = 0;
//                    return $this->setError("您名下未查找到该客户，请确认客户信息或前往CRM系统维护客户信息");
                }

                //插入询价主表
                $inquiry_sn = InquiryService::getUniqueInquirySn();
                $inquiry_id = InquiryService::createInquiry($inquiry_param['delivery_place'],
                    $inquiry_param['user_name'],
                    $inquiry_sn,
                    $inquiry_param['user_id'] ?? 0, $inquiry_param['com_id'] ?? 0, $inquiry_param['com_name'] ?? '',
                    $inquiry_param['user_types'] ?? InquiryModel::USER_TYPE_SINGLE,
                    $inquiry_param['inquiry_create_user_id'],
                    $inquiry_param['inquiry_create_user_name']);


                //构造明细param
                $inquiry_item_param = [
                    'inquiry_id' => $inquiry_id,
                    'goods_name' => $inquiry_param['goods_name'],
                    'brand_id' => $standard_brand_id,
                    'brand_name' => $inquiry_param['brand_name'],
                    'inquiry_number' => $inquiry_param['inquiry_number'],
                    'delivery_time' => $inquiry_param['delivery_time'],
                    'deadline_time' => ($inquiry_param['deadline_time']) ? strtotime($inquiry_param['deadline_time']) : 0,
                    'batch' => $inquiry_param['batch'],
                    'remark' => $inquiry_param['remark'],
                    'demand_type' => InquiryItemModel::DEMAND_TYPE_WEBINQUIRY,
                    'update_time' => time(),
                    'create_time' => time(),
                    'create_uid' => $inquiry_param['inquiry_create_user_id'],
                    'create_name' => $inquiry_param['inquiry_create_user_name'],
                    'web_inquiry_sn' => $web_inquiry_info['web_inquiry_sn'],
                    'web_inquiry_item_id' => $web_inquiry_item['id'],
                    'match_sku_id' => $web_inquiry_item['sku_id'] ? $web_inquiry_item['sku_id'] : "",
                ];

                //插入明细
                $inquiry_item_id = (new InquiryItemModel())->addInquiryItem($inquiry_item_param);
                $multi_inquiry_id_map[] = [
                    "inquiry_id" => $inquiry_id,
                    "inquiry_item_id" => $inquiry_item_id,
                    'goods_name' => $inquiry_param['goods_name'],
                    'brand_name' => $inquiry_param['brand_name'],
                    'match_sku_id' => $inquiry_item_param['match_sku_id'],
                    'is_special_inquiry' => InquiryItemModel::IS_SKU_INQUIRY_NO,
                    'create_uid' => $inquiry_param['inquiry_create_user_id'],
                ];

                // 更新绑定关系
                WebInquiryItemsModel::updateById([
                    'rela_inquiry_sn' => $inquiry_sn,
                    'rela_inquiry_item_id' => $inquiry_item_id,
                ], $web_inquiry_item['id']);
            }

            //添加成功
            DB::connection("rfq")->commit();
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("rfq")->rollBack();
            return $this->setError($exception->getMessage());
        }

        // 处理队列，需要投队列，进行后续操作
        $RabbitQueueModel = new RabbitQueueModel();
        foreach ($multi_inquiry_id_map as $multi_i => $inquiry_id_info) {
            if ($inquiry_id_info['match_sku_id']) {
                // 生成指定sku_id报价
                $RabbitQueueModel->insertQueue('/queue/quote/autoQuoteBySkuId', [
                    "inquiry_items_id" => $inquiry_id_info['inquiry_item_id'],
                    "match_sku_id" => $inquiry_id_info['match_sku_id'],
                ], $RabbitQueueModel::QUEUE_FRQ);
            } else {
                // 匹配强履约数据
                $RabbitQueueModel->insertQueue('/queue/quote/autoQuoteByAbility', [
                    "inquiry_items_id" => $inquiry_id_info['inquiry_item_id'],
                ], $RabbitQueueModel::QUEUE_FRQ);
            }

            $RabbitQueueModel->insertQueue('/queue/user/incUserExtendNum', [
                "inquiry_num" => 1,
                "user_id" => $inquiry_id_info['create_uid'],
            ], $RabbitQueueModel::QUEUE_FRQ);
            $RabbitQueueModel->insertQueue('/queue/tax/addOpenItems', [
                'data' => [
                    "goods_name" => $inquiry_id_info['goods_name'],
                    "brand_name" => $inquiry_id_info['brand_name'],
                ],
            ], $RabbitQueueModel::QUEUE_FRQ);
        }

        return $this->setSuccess("生成询价单成功");
    }

    // 网站询价明细列表
    public function getWebInquiryItemList(Request $request)
    {
        $web_inquiry_id = $request->input("web_inquiry_id");
        $web_inquiry_info = WebInquiryModel::getWebInquiryById($web_inquiry_id);
        if (empty($web_inquiry_info)) {
            return $this->setError("网站询价单查询失败");
        }

        $web_inquiry_items = WebInquiryItemsModel::getListByWebInquiryId($web_inquiry_id);
        $web_inquiry_confirmed_quotes = WebInquiryConfirmedQuoteModel::getListByWebInquiryId($web_inquiry_id);
        $web_inquiry_confirmed_quote_map = array_column($web_inquiry_confirmed_quotes, null, 'web_inquiry_items_id');

        $format_web_inquiry_item_list = [];
        if ($web_inquiry_items) {
            $inquiry_item_map = [];
            $inquiry_item_ids = array_column($web_inquiry_items, 'rela_inquiry_item_id');
            $inquiry_item_ids = array_values(array_unique(array_filter($inquiry_item_ids)));
            if ($inquiry_item_ids) {
                $inquiry_item_list = InquiryItemModel::getInquiryItemsByIds($inquiry_item_ids);
                $inquiry_item_map = array_column($inquiry_item_list, null, 'id');
            }

            foreach ($web_inquiry_items as &$web_inquiry_item) {
                $rela_inquiry_item = isset($inquiry_item_map[$web_inquiry_item['rela_inquiry_item_id']]) ? $inquiry_item_map[$web_inquiry_item['rela_inquiry_item_id']] : [];
                $web_inquiry_item['rela_inquiry_stauts'] = ($rela_inquiry_item) ? $rela_inquiry_item['status'] : 0;
                $web_inquiry_item['rela_inquiry_stauts_val'] = ($rela_inquiry_item) ? NameConvert::getInquiryStatusName($rela_inquiry_item['status']) : '';
                $web_inquiry_item['currency_val'] = NameConvert::getCurrencyName($web_inquiry_item['currency']);
                $web_inquiry_item['item_status_val'] = NameConvert::getWebInquiryItemStatusName($web_inquiry_item['item_status']);
                $web_inquiry_item['verify_deadline_time'] = NameConvert::getDateTime($web_inquiry_item['verify_deadline_time']);
                $web_inquiry_item['verify_delivery_info'] = NameConvert::getDeliveryInfo($web_inquiry_item['verify_delivery_time']);
                $web_inquiry_item['delivery_info'] = NameConvert::getDeliveryInfo($web_inquiry_item['delivery_time']);
                $sale_confirmed_quote = isset($web_inquiry_confirmed_quote_map[$web_inquiry_item['id']]) ? $web_inquiry_confirmed_quote_map[$web_inquiry_item['id']] : [];
                if ($sale_confirmed_quote) {
                    $sale_confirmed_quote['currency_val'] = NameConvert::getCurrencyName($sale_confirmed_quote['currency']);
                    $sale_confirmed_quote['expire_time'] = NameConvert::getDateTime($sale_confirmed_quote['expire_time']);
                    $sale_confirmed_quote['create_time'] = NameConvert::getDateTime($sale_confirmed_quote['create_time']);
                }
                $web_inquiry_item['sale_confirmed_quote'] = [$sale_confirmed_quote];
                $web_inquiry_item['sale_confirmed_price'] = ($sale_confirmed_quote) ? $sale_confirmed_quote['quote_price'] : '-';
                $format_web_inquiry_item_list[] = $web_inquiry_item;
            }
        }

        $data = [
            'list' => $format_web_inquiry_item_list
        ];
        return $this->setSuccess($data);
    }

    // 获取网站询价明细确认信息
    public function getConfirmItemList(Request $request)
    {
        $web_inquiry_item_ids = $request->input("web_inquiry_item_ids");
        $web_inquiry_item_ids = explode(",", $web_inquiry_item_ids);
        $web_inquiry_item_ids = array_values(array_unique(array_filter($web_inquiry_item_ids)));
        if (empty($web_inquiry_item_ids)) {
            return $this->setError("请选择确认明细");
        }

        // 网站询价列表
        $web_inquiry_items = WebInquiryItemsModel::getListByIds($web_inquiry_item_ids);

        // 网站询价信息
        $web_inquiry_info = WebInquiryModel::getWebInquiryById($web_inquiry_items[0]['web_inquiry_id']);

        // 网站询价确认列表
        $web_inquiry_confirmed_quotes = WebInquiryConfirmedQuoteModel::getListByWebInquiryItemIds($web_inquiry_item_ids);
        $web_inquiry_confirmed_quote_map = array_column($web_inquiry_confirmed_quotes, null, 'web_inquiry_items_id');
        // 询价单报价列表
        $inquiry_item_ids = array_column($web_inquiry_items, 'rela_inquiry_item_id');
        $inquiry_item_ids = array_values(array_unique(array_filter($inquiry_item_ids)));
        $inquiry_quote_map = [];
        if ($inquiry_item_ids) {
            $selected_quote_list = QuoteSelectedModel::getByInquiryItemIds($inquiry_item_ids);
            if ($selected_quote_list) {
                $quote_ids = array_column($selected_quote_list, 'quote_id');
                $quote_list = QuoteModel::getListByIds($quote_ids);
                if ($quote_list) {
                    $inquiry_quote_map = array_column($quote_list, null, 'inquiry_items_id');
                }
            }
        }
        $to_confirm_list = [];
        foreach ($web_inquiry_items as $web_inquiry_item) {
            $buyer_quote_info = isset($inquiry_quote_map[$web_inquiry_item['rela_inquiry_item_id']]) ? $inquiry_quote_map[$web_inquiry_item['rela_inquiry_item_id']] : [];
            $sale_confirmed_quote = isset($web_inquiry_confirmed_quote_map[$web_inquiry_item['id']]) ? $web_inquiry_confirmed_quote_map[$web_inquiry_item['id']] : [];
            if ($sale_confirmed_quote) {
                $fmt_sale_confirmed_quote = WebInquiryService::getFormatSaleConfirmedQuote($sale_confirmed_quote);
            } else {
                $fmt_sale_confirmed_quote = WebInquiryService::getFormatDefaultSaleConfirmedQuote($buyer_quote_info);
            }

            $to_confirm_list[] = [
                'web_inquiry_item_info' => (object)WebInquiryService::getFormatWebInquiryItemInfo($web_inquiry_item),
                'buyer_quote_info' => (object)WebInquiryService::getFormatBuyerQuote($buyer_quote_info),
                'sale_confirmed_quote' => (object)$fmt_sale_confirmed_quote,
            ];
        }

        $web_inquiry_aggr_count = WebInquiryService::getWebInquiryAggrCount($web_inquiry_info['web_inquiry_id']);
        $data = [
            'web_inquiry_info' => [
                'web_inquiry_sn' => $web_inquiry_info['web_inquiry_sn'],
                'web_inquiry_id' => $web_inquiry_info['web_inquiry_id'],
                'total_item_count' => $web_inquiry_aggr_count ? $web_inquiry_aggr_count['total_item_count'] : 0,
                'quoted_count' => WebInquiryService::getWebInquiryQuotedCount($web_inquiry_info['web_inquiry_id']),
                'puched_count' => WebInquiryService::getWebInquiryPuchedCount($web_inquiry_info['web_inquiry_id']),
            ],
            'list' => $to_confirm_list
        ];
        return $this->setSuccess($data);
    }

    // 网站询价确认
    public function confirmItems(Request $request)
    {
        $multi_confirm_list_json = $request->input('multi_confirm_list');
        $is_push_customer = $request->input("is_push_customer", 0);
        $multi_confirm_list = json_decode($multi_confirm_list_json, true);
        if (empty($multi_confirm_list)) {
            return $this->setError("请添加询价确认信息");
        }

        $validator = Validator::make(['multi_confirm_list' => $multi_confirm_list], [
            'multi_confirm_list' => 'required|array',
            'multi_confirm_list.*.web_inquiry_item_id' => 'required',
            'multi_confirm_list.*.quote_id' => 'required',
            'multi_confirm_list.*.goods_name' => 'required',
            'multi_confirm_list.*.brand_name' => 'required',
            'multi_confirm_list.*.quote_number' => 'required',
            'multi_confirm_list.*.goods_price' => 'required',
            'multi_confirm_list.*.delivery_time' => 'present',
            'multi_confirm_list.*.batch' => 'present',
            'multi_confirm_list.*.remark' => 'present',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $web_inquiry_items_ids = array_column($multi_confirm_list, 'web_inquiry_item_id');
        $web_inquiry_items_list = WebInquiryItemsModel::getListByIds($web_inquiry_items_ids);
        if (empty($web_inquiry_items_list)) {
            return $this->setError("网站询价明细信息查询失败");
        }
        $web_inquiry_items_map = array_column($web_inquiry_items_list, null, 'id');

        $quote_ids = array_column($multi_confirm_list, 'quote_id');
        $quote_list = QuoteModel::getListByIds($quote_ids);
        $quote_list_map = array_column($quote_list, null, 'id');

        // 创建确认单
        try {
            DB::connection("rfq")->beginTransaction();
            foreach ($multi_confirm_list as $item) {
                $web_inquiry_item_info = $web_inquiry_items_map[$item['web_inquiry_item_id']];
                $quote_info = $quote_list_map[$item['quote_id']];
                $confirmed_info = WebInquiryConfirmedQuoteModel::getConfirmInfoByWebInquiryItemId($item['web_inquiry_item_id']);
                // 如果存在确认信息，那么就修改，如果不存在，那么就创建确认信息
                $standard_brand_id = InquiryService::getStandardBrandId($item['brand_name']);
                $common_confirm_info = [
                    'web_inquiry_id' => $web_inquiry_item_info['web_inquiry_id'],
                    'web_inquiry_items_id' => $item['web_inquiry_item_id'],
                    'goods_name' => $item['goods_name'],
                    'brand_id' => $standard_brand_id,
                    'brand_name' => $item['brand_name'],
                    'quote_price' => $item['goods_price'],
                    'quote_number' => $item['quote_number'],
                    'currency' => $item['currency'],
                    'delivery_time' => (string)$item['delivery_time'],
                    'batch' => (string)$item['batch'],
                    'remark' => (string)$item['remark'],
                    'quote_id' => $item['quote_id'],
                    'expire_time' => (string)$quote_info['expire_time'],
                    'confirmed_status' => ($is_push_customer) ? 1 : 0,
                ];
                if ($confirmed_info) {
                    $update_confirm_info = array_merge($common_confirm_info, [
                        'update_time' => time(),
                    ]);
                    WebInquiryConfirmedQuoteModel::updateById($update_confirm_info, $confirmed_info['id']);
                } else {
                    $add_confirm_info = array_merge($common_confirm_info, [
                        'create_name' => $request->user->name,
                        'create_time' => time(),
                    ]);
                    WebInquiryConfirmedQuoteModel::createConfirmItem($add_confirm_info);
                }

                //更新平台网站询价状态
                BomMatchService::updateLiexinInquiryItemQuoteStatus($web_inquiry_item_info['source_item_id']);
            }

            //网站询价明细标记为确认报价
            WebInquiryItemsModel::setConfirmed($web_inquiry_items_ids);

            DB::connection("rfq")->commit();
            return $this->setSuccess("网站询价确认成功");
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            DB::connection("rfq")->rollBack();
            return $this->setError($exception->getMessage());
        }
        return $this->setSuccess("ok");
    }

    // 绑定客户销售
    public function bindCustomerSales(Request $request)
    {
        $bind_sale_list = $request->input("bind_sale_list");
        $validator = Validator::make(['bind_sale_list' => $bind_sale_list], [
            'bind_sale_list' => 'required|array',
            'bind_sale_list.*.customer_id' => 'required',
            'bind_sale_list.*.sale_id' => 'required',
            'bind_sale_list.*.org_id' => 'required',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        // 查询绑定
        foreach ($bind_sale_list as $bind_sale) {
            if ($bind_sale['org_id'] != UserModel::ORG_LIEXINKEJI) {
                continue;
            }

            $cms_user_info = CmsUserInfoModel::getUserInfoById($bind_sale['sale_id']);
            WebInquiryModel::updateByUserId([
                'create_uid' => $bind_sale['sale_id'],
                'create_name' => $cms_user_info ? $cms_user_info['name'] : '',
            ], $bind_sale['customer_id']);
        }
        return $this->setSuccess("绑定客户销售成功");
    }


}
