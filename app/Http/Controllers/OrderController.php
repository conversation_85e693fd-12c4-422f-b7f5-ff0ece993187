<?php

namespace App\Http\Controllers;

use App\Exports\QuoteOrderExcel;
use App\Http\Models\BomItemsModel;
use App\Http\Models\BomModel;
use App\Http\Models\BrandStandardModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\InquiryRelaBomModel;
use App\Http\Models\InquiryToOrderModel;
use App\Http\Models\Order\OrderModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\QuoteOrderItemModel;
use App\Http\Models\QuoteOrderModel;
use App\Http\Models\QuoteSelectedModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\BomService;
use App\Http\Services\BrandService;
use App\Http\Services\InquiryService;
use App\Http\Services\OrderService;
use App\Http\Services\PermService;
use App\Http\Services\PriceService;
use App\Http\Services\UserSysMsgService;
use App\Http\Utils\ErrMsg;
use App\Http\Utils\NameConvert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class OrderController extends Controller
{

    public function getOrderCustomerPhone(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递订单id");
        }

        $quote_order = QuoteOrderModel::getById($id);

        if (!$quote_order) {
            return $this->setError("该报价单不存在");
        }

        return $this->setSuccess([
            'account' => $quote_order['client_name'] ?? '',
        ]);
    }

    // 报价单列表
    public function orderList(Request $request)
    {
        $userInfo = CmsUserInfoModel::getUserInfoById($request->user->userId);
        $is_online_saler = in_array($userInfo['department_id'], config('website.online_sale_department_ids')) ? 1 : 0;

        return view('order.orderList', ['is_online_saler' => $is_online_saler]);
    }

    // 报价单详情
    public function detail(Request $request)
    {
        $order_id = $request->input('order_id');
        if (empty($order_id)) {
            return $this->setError("请传递订单id");
        }

        $order_info = QuoteOrderModel::getById($order_id);
        if (empty($order_info)) {
            return $this->setError("报价单不存在");
        }

        // 转换名称
        $order_info['o_status'] = NameConvert::getOrderStatusName($order_info['o_status']);
        $order_info['currency'] = NameConvert::getCurrencyName($order_info['currency']);
        $order_info['delivery_place'] = NameConvert::getDeliveryTypeName($order_info['delivery_place']);
        $order_info['create_time'] = NameConvert::getDateTime($order_info['create_time']);

        $order_items = QuoteOrderItemModel::getListByOrderSn($order_info['order_sn']);
        $data = [];
        $data['order_info'] = $order_info;
        $data['order_items'] = $order_items;
        return view('order.detail', $data);
    }

    // 报价单详情(json输出)
    public function getOrderInfo(Request $request)
    {
        $order_id = $request->input('order_id');
        if (empty($order_id)) {
            return $this->setError("请传递订单id");
        }

        $order_info = QuoteOrderModel::getById($order_id);
        if (empty($order_info)) {
            return $this->setError("报价单不存在");
        }

        // 转换名称
        $order_info['o_status'] = NameConvert::getOrderStatusName($order_info['o_status']);
        $order_info['currency'] = NameConvert::getCurrencyName($order_info['currency']);
        $order_info['delivery_place'] = NameConvert::getDeliveryTypeName($order_info['delivery_place']);
        $order_info['create_time'] = NameConvert::getDateTime($order_info['create_time']);

        $order_items = QuoteOrderItemModel::getListByOrderSn($order_info['order_sn']);
        $brandStandIdMap = BrandService::getStandardBrandIdMap(array_column($order_items, 'brand_name'));
        foreach ($order_items as &$item) {
            $item['standard_brand_id'] = $brandStandIdMap[$item['brand_name']];
            $item['standard_brand_name'] = $item['brand_name'];
        }
        $data = [];
        $data['order_info'] = $order_info;
        $data['order_items'] = $order_items;
        return $this->setSuccess($data);
    }

    public function getInfo(Request $request)
    {
        $order_id = $request->input('order_id');
        if (empty($order_id)) {
            return $this->setError("请传递订单id");
        }

        $order_info = QuoteOrderModel::getById($order_id);
        if (empty($order_info)) {
            return $this->setError("报价单不存在");
        }

        $order_items = QuoteOrderItemModel::getListByOrderSn($order_info['order_sn']);

        $data = [];
        $data['order_info'] = $order_info;
        $data['order_items'] = $order_items;
        return $this->setSuccess($data);
    }

    public function addOrder(Request $request)
    {
        $params = [
            'client_name' => $request->input("client_name"),
            'delivery_place' => $request->input("delivery_place"),
            'order_items' => $request->input("order_items"),
            'currency_id' => $request->input("currency_id"),
            'from_type' => $request->input("from_type", 'frq'),
        ];
        $validator = Validator::make($params, [
            'currency_id' => 'required',
            'client_name' => 'required',
            'delivery_place' => 'required|in:1,2',
            'order_items' => 'required|json',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors()->toJson(JSON_UNESCAPED_UNICODE);
            return $this->setError($errors);
        }

        $order_items = json_decode($params['order_items'], true);
        foreach ($order_items as &$item) {
            if (!isset($item['rela_id'])) {
                return $this->setError("请传递rela_id");
            }
            if (!isset($item['client_price_origin'])) {
                return $this->setError("请传递客户报价");
            }
            if (!isset($item['rate']) || !is_numeric($item['rate'])) {
                return $this->setError("请传递利润点");
            }
            // 强制转换格式
            $item['client_price_origin'] = (float)$item['client_price_origin'];
            $item['rate'] = (int)$item['rate'];
        }

        $order_sn = OrderService::getUniqueOrderSn();
        // $client_currency = ($params['delivery_place'] == 1) ? 1 : 2;
        $client_currency = $params['currency_id'];
        $total_price_origin = 0;
        $total_price_rmb = 0;
        $total_cost_rmb = 0;
        $rela_ids = array_column($order_items, "rela_id");
        if ($params['from_type'] == "bom") {
            $bom_items_map = BomService::getBomItemListMap($rela_ids);
            $rela_inquiry_bom_item_ids = [];
            $bom_id = 0;
            foreach ($bom_items_map as $bom_item) {
                if ($bom_item['is_inquiry'] == BomItemsModel::IS_INQUIRY_YES) {
                    $rela_inquiry_bom_item_ids[] = $bom_item['id'];
                }
                $bom_id = $bom_item['bom_id'];
            }

            $bom_info = BomModel::getById($bom_id);
            if ($rela_inquiry_bom_item_ids) {
                $inquiry_rela_bom_list = InquiryRelaBomModel::getListByBomItemIds($rela_inquiry_bom_item_ids);
                $bom_item_id_rela_inquiry_item_id_map = array_column($inquiry_rela_bom_list, 'inquiry_item_id',
                    'bom_item_id');
                $inquiry_item_ids = array_column($inquiry_rela_bom_list, 'inquiry_item_id');
                $inquiry_item_select_quote_map = InquiryService::getInquirySelectQuoteMap($inquiry_item_ids);
            }
        } else {
            $inquiry_items_map = InquiryService::getInquiryItemListMap($rela_ids);
            $inquiry_list_map = InquiryService::getInquiryListMap(array_column($inquiry_items_map, "inquiry_id"));
            $inquiry_item_select_quote_map = InquiryService::getInquirySelectQuoteMap($rela_ids);
        }

        foreach ($order_items as $order_item) {
            if ($params['from_type'] == 'bom') {
                $rela_item_info = $bom_items_map[$order_item['rela_id']];
                $rela_inquiry_item_id = isset($bom_item_id_rela_inquiry_item_id_map[$order_item['rela_id']]) ? $bom_item_id_rela_inquiry_item_id_map[$order_item['rela_id']] : 0;
                $inquiry_select_quote_info = isset($inquiry_item_select_quote_map[$rela_inquiry_item_id]) ? $inquiry_item_select_quote_map[$rela_inquiry_item_id] : [];
                $currency = ($bom_info['delivery_place'] == BomModel::DELIVERY_PLACE_CN) ? PriceService::CURRENCY_TYPE_RMB : PriceService::CURRENCY_TYPE_US;
            } else {
                $rela_item_info = $inquiry_items_map[$order_item['rela_id']];
                $inquiry_select_quote_info = isset($inquiry_item_select_quote_map[$order_item['rela_id']]) ? $inquiry_item_select_quote_map[$order_item['rela_id']] : [];
                $currency = $inquiry_list_map[$inquiry_items_map[$order_item['rela_id']]['inquiry_id']]['currency'];
            }
            $quote_price_origin = ($inquiry_select_quote_info) ? $inquiry_select_quote_info['price_origin'] : 0;
            $quote_price_rmb = ($inquiry_select_quote_info) ? $inquiry_select_quote_info['price_rmb'] : 0;
            $price_rmb = PriceService::getRmbPrice($order_item['client_price_origin'], $client_currency);
            $order_item_info = [
                "inquiry_item_id" => ($params['from_type'] == 'bom') ? 0 : $order_item['rela_id'],
                'rela_id' => $order_item['rela_id'],
                "order_sn" => $order_sn,
                "goods_name" => $rela_item_info['goods_name'],
                "brand_name" => $rela_item_info['brand_name'],
                "quote_number" => $rela_item_info['inquiry_number'],
                "batch" => $rela_item_info['batch'],
                "delivery_time" => $rela_item_info['delivery_time'],
                "currency" => $currency,
                "price_origin" => $quote_price_origin,
                "price_rmb" => $quote_price_rmb,
                "rate" => $order_item['rate'],
                "client_currency" => $client_currency,
                "client_price_origin" => $order_item['client_price_origin'],
                "client_price_rmb" => $price_rmb,
                "total_price_origin" => $rela_item_info['inquiry_number'] * $order_item['client_price_origin'],
                "total_price_rmb" => $rela_item_info['inquiry_number'] * $price_rmb,
                "create_uid" => $request->user->userId,
                "create_name" => $request->user->name,
                "create_time" => time(),
            ];

            // 总成本累积
            $total_cost_rmb = $total_cost_rmb + ($rela_item_info['inquiry_number'] * $quote_price_rmb);

            // 总价格累加
            $total_price_origin = $total_price_origin + $order_item_info['total_price_origin'];
            $total_price_rmb = $total_price_rmb + $order_item_info['total_price_rmb'];
            QuoteOrderItemModel::addOrderItem($order_item_info);
        }
        $order_info = [
            "order_sn" => $order_sn,
            "client_name" => $params['client_name'],
            "delivery_place" => $params['delivery_place'],
            "from_type" => $params['from_type'],
            "currency" => $client_currency,
            "total_price_origin" => $total_price_origin,
            "total_price_rmb" => $total_price_rmb,
            "rate" => ($total_cost_rmb) ? (($total_price_origin / $total_cost_rmb) - 1) * 100 : 0,
            "item_num" => count($order_items),
            "o_status" => QuoteOrderModel::STATUS_NORMAL,
            "create_uid" => $request->user->userId,
            "create_name" => $request->user->name,
            "create_time" => time(),
        ];

        $res = QuoteOrderModel::addOrder($order_info);
        if (empty($res)) {
            return $this->setError("保存失败");
        }
        return $this->setSuccess("保存成功");

    }

    public function updateOrderTransfer(Request $request)
    {
        $id = $request->input("frq_order_id");
        if (empty($id)) {
            return $this->setError("id不存在");
        }
        $result = OrderService::updateTransferTimes($id);
        if ($result === false) {
            return $this->setError("更新失败");
        }
        return $this->setSuccess("更新成功");
    }

    public function orderPdf(Request $request)
    {
        $order_id = $request->input('order_id');
        if (empty($order_id)) {
            return $this->setError("请传递订单id");
        }

        $order_info = QuoteOrderModel::getById($order_id);
        if (empty($order_info)) {
            return $this->setError("报价单不存在");
        }

        // 转换名称
        $order_info['o_status'] = NameConvert::getOrderStatusName($order_info['o_status']);
        $order_info['currency_code'] = NameConvert::getCurrencyCode($order_info['currency']);
        $order_info['currency'] = NameConvert::getCurrencyName($order_info['currency']);
        $order_info['delivery_place'] = NameConvert::getDeliveryTypeName($order_info['delivery_place']);
        $order_info['create_time'] = NameConvert::getDateTime($order_info['create_time']);

        if ($order_info['delivery_place'] == '大陆') {
            $order_info['quote_str'] = 'RMB含税';
        } else {
            $order_info['quote_str'] = $order_info['currency_code'] . '未税';
        }

        $order_items = QuoteOrderItemModel::getListByOrderSn($order_info['order_sn']);
        $data = [];
        $data['order_info'] = $order_info;
        $data['order_items'] = $order_items;
        $data['create_info'] = CmsUserInfoModel::getUserInfo($order_info['create_uid']);
        $html = view('order.orderPdf', $data)->render();
        if ($request->input('debug') == 1) {
            return $html;
        }
        $pdf = App::make('dompdf.wrapper');
        $name = 'service_' . time() . '.pdf';
        $pdf->loadHTML($html)
            ->setPaper('a4', 'landscape')//横列landscape ,  竖列 portrait
            ->setWarnings(true);
        return $pdf->stream($name);
    }

    public function orderExcel(Request $request)
    {
        $order_id = $request->input('order_id');
        if (empty($order_id)) {
            return $this->setError("请传递订单id");
        }

        $order_info = QuoteOrderModel::getById($order_id);
        if (empty($order_info)) {
            return $this->setError("报价单不存在");
        }

        $filename = '报价单_' . $order_info['order_sn'] . '.xlsx';

        $order_excel = new QuoteOrderExcel($order_info);
        // return $order_excel->view();
        return Excel::download($order_excel, $filename);
    }

    // 获取用户报价列表
    public function getOrderList(Request $request)
    {
        $params = [
            'page' => $request->input("page", 1),
            'limit' => $request->input("limit", 10),
            'search_date' => $request->input("search_date"),
            // 'start_time' => $request->input("start_time"),
            // 'end_time' => $request->input("end_time"),
            'o_status' => $request->input("o_status"),
            'create_name' => $request->input('create_name'),
            'client_name' => $request->input('client_name'),
        ];
        $validator = Validator::make($params, [
            'page' => 'required|numeric',
            'limit' => 'required|numeric',
            'status' => 'nullable|numeric',
            // 'start_time' => 'nullable|date',
            // 'end_time' => 'nullable|date',
        ]);

        if ($validator->fails()) {
            $errors = $validator->errors()->toJson(JSON_UNESCAPED_UNICODE);
            return $this->setError($errors);
        }

        $params['create_uid'] = $request->user->userId;
        $roleList = PermService::getUserRoles($request->user->userId, $request->user->email);
        $where = $this->buildSearchWhere($params, $roleList);
        $order_list = QuoteOrderModel::getListByWhere($where, $params['page'], $params['limit']);
        if ($order_list && $order_list['data']) {
            foreach ($order_list['data'] as &$order) {
                $order['o_status'] = NameConvert::getOrderStatusName($order['o_status']);
                $order['create_time'] = NameConvert::getDateTime($order['create_time']);

                $order['is_encrypted'] = false;
                if (str_contains($order['client_name'], '@') || preg_match('/^[0-9\-]+$/', $order['client_name'])) {
                    $order['client_name'] = NameConvert::encryptAccount($order['client_name']);
                    $order['is_encrypted'] = true;
                }
            }
        }
        $data = [
            "list" => $order_list['data'],
            "total" => isset($order_list['total']) ? $order_list['total'] : 0,
        ];
        return $this->setSuccess($data);
    }

    // 获取报价信息
    public function getInquiryToOrderItems(Request $request)
    {
        $inquiry_items_ids = $request->input('inquiry_items_ids');
        $inquiry_items_ids = explode(",", $inquiry_items_ids);
        $inquiry_items_ids = array_values(array_filter($inquiry_items_ids));
        if (empty($inquiry_items_ids)) {
            return $this->setError("请传递询价id");
        }

        $inquiry_items_map = InquiryService::getInquiryItemListMap($inquiry_items_ids);
        $inquiry_list_map = InquiryService::getInquiryListMap(array_column($inquiry_items_map, "inquiry_id"));
        $select_list = QuoteSelectedModel::getByInquiryItemIds($inquiry_items_ids);

        $inquiry_item_select_quote_map = [];
        if ($select_list) {
            $quote_ids = array_column($select_list, 'quote_id');
            $quote_list = QuoteModel::getListByIds($quote_ids);
            foreach ($quote_list as &$quote) {
                $inquiry_item_select_quote_map[$quote['inquiry_items_id']] = $quote;
            }
        }
        $order_items = [];
        if ($inquiry_items_map) {
            $standard_brand_list = BrandStandardModel::getBrandListByNames(array_column($inquiry_items_map,
                'brand_name'));
            $standard_brand_name_info_map = [];
            if ($standard_brand_list) {
                $standard_brand_name_info_map = array_column($standard_brand_list, null, 'brand_name');
            }
            foreach ($inquiry_items_map as $inquiry_item_info) {
                $inquiry_info = isset($inquiry_list_map[$inquiry_item_info['inquiry_id']]) ? $inquiry_list_map[$inquiry_item_info['inquiry_id']] : [];
                $standard_brand_info = isset($standard_brand_name_info_map[$inquiry_item_info['brand_name']]) ? $standard_brand_name_info_map[$inquiry_item_info['brand_name']] : [];
                $brand_area = isset($standard_brand_info['brand_area']) ? $standard_brand_info['brand_area'] : 0;
                $order_item_info = [
                    "inquiry_id" => isset($inquiry_info['id']) ? $inquiry_info['id'] : 0,
                    "inquiry_sn" => isset($inquiry_info['inquiry_sn']) ? $inquiry_info['inquiry_sn'] : 0,
                    "inquiry_item_id" => $inquiry_item_info['id'],
                    'user_id' => isset($inquiry_info['user_id']) ? $inquiry_info['user_id'] : 0,
                    'customer_id' => isset($inquiry_info['com_id']) ? $inquiry_info['com_id'] : 0,
                    'customer_name' => isset($inquiry_info['user_name']) ? $inquiry_info['user_name'] : 0,
                    'brand_id' => $inquiry_item_info['brand_id'],
                    'brand_name' => $inquiry_item_info['brand_name'],
                    'standard_brand_id' => isset($standard_brand_info['standard_brand_id']) ? $standard_brand_info['standard_brand_id'] : 0,
                    'standard_brand_name' => $inquiry_item_info['brand_name'],
                    'brand_area' => $brand_area,
                    'brand_area_cn' => NameConvert::getBrandAreaCn($brand_area),
                    'sku_id' => "",
                    "goods_type" => 1,

                ];
                $quote_info = isset($inquiry_item_select_quote_map[$inquiry_item_info['id']]) ? $inquiry_item_select_quote_map[$inquiry_item_info['id']] : [];
                if ($quote_info) {
                    if ($quote_info['quote_type'] == QuoteModel::QUOTE_TYPE_SELFGOODS) {
                        $order_item_info['goods_type'] = 2;
                        $order_item_info['supplier_id'] = 0;
                        $order_item_info['supplier_name'] = "猎芯自营";
                    } else {
                        $order_item_info['supplier_id'] = 17;
                        $order_item_info['supplier_name'] = "猎芯专营";
                    }
                    $order_item_info['quote_id'] = $quote_info['id'];
                    $order_item_info['quote_sn'] = $quote_info['quote_sn'];
                    $order_item_info['goods_name'] = $quote_info['goods_name'];
                    $order_item_info['goods_number'] = $quote_info['quote_number'];
                    $order_item_info['buyer_id'] = $quote_info['create_uid'];
                    $order_item_info['buyer_name'] = $quote_info['create_name'];
                    $order_item_info['initial_price'] = $quote_info['price_origin'];
                    $order_item_info['goods_price'] = $quote_info['price_rmb'];
                    $order_item_info['currency'] = $quote_info['currency'];
                    $order_item_info['batch'] = $quote_info['batch'];
                    $order_item_info['price_origin'] = $quote_info['price_origin'];
                    $order_item_info['price_rmb'] = $quote_info['price_rmb'];
                    $order_item_info['pm_uid'] = $quote_info['pm_uid'];
                    $order_item_info['pm_name'] = $quote_info['pm_name'];
                    $order_item_info['quote_type'] = $quote_info['quote_type'];
                    $order_item_info['ability_level'] = $quote_info['ability_level'];
                    $deliver_info = NameConvert::getDeliveryInfo($quote_info['delivery_time']);
                    $order_item_info['delivery_time'] = $deliver_info['delivery_val'];
                    $order_item_info['delivery_time_num'] = $deliver_info['delivery_int'];
                    $order_item_info['delivery_time_unit'] = $deliver_info['delivery_unit_val'];
                    $order_item_info['sku_id'] = ($quote_info['goods_id']) ? strval($quote_info['goods_id']) : "";
                } else {
                    $order_item_info['quote_id'] = 0;
                    $order_item_info['quote_sn'] = "";
                    $order_item_info['goods_name'] = $inquiry_item_info['goods_name'];
                    $order_item_info['goods_number'] = $inquiry_item_info['inquiry_number'];
                    $order_item_info['supplier_id'] = 17;
                    $order_item_info['supplier_name'] = "猎芯专营";
                    $order_item_info['buyer_id'] = 0;
                    $order_item_info['buyer_name'] = "";
                    $order_item_info['initial_price'] = 0;
                    $order_item_info['goods_price'] = 0;
                    $order_item_info['price_origin'] = 0;
                    $order_item_info['price_rmb'] = 0;
                    $order_item_info['pm_uid'] = 0;
                    $order_item_info['pm_name'] = '';
                    $order_item_info['quote_type'] = 0;
                    $order_item_info['currency'] = $inquiry_list_map[$inquiry_item_info['inquiry_id']]['currency'];
                    $order_item_info['batch'] = $inquiry_item_info['batch'];
                    $order_item_info['ability_level'] = -1;
                    $deliver_info = NameConvert::getDeliveryInfo($inquiry_item_info['delivery_time']);
                    $order_item_info['delivery_time'] = $deliver_info['delivery_val'];
                    $order_item_info['delivery_time_num'] = $deliver_info['delivery_int'];
                    $order_item_info['delivery_time_unit'] = $deliver_info['delivery_unit_val'];
                }
                $order_items[] = $order_item_info;
            }
        }

        $data = [
            "list" => array_values($order_items),
        ];
        return $this->setSuccess($data);
    }

    // 询价明细绑定销售订单
    public function bindSaleOrder(Request $request)
    {
        $params = [
            'inquiry_item_ids' => $request->input("inquiry_item_ids"),
            'order_id' => $request->input("order_id"),
            'order_sn' => $request->input("order_sn"),
            'create_uid' => $request->input("create_uid"),
        ];

        $validator_msg = [
            'inquiry_item_ids.required' => '询价明细ids必填',
            'order_id.required' => '订单id必填',
            'order_sn.required' => '订单编号必填',
            'create_uid.required' => '创建人必填',
        ];
        $validator = Validator::make($params, [
            'inquiry_item_ids' => "required",
            'order_id' => "required",
            'order_sn' => "required",
            'create_uid' => "required",
        ], $validator_msg);

        if ($validator->fails()) {
            $errors = $validator->errors()->first();
            return $this->setError($errors);
        }

        DB::connection('rfq')->beginTransaction();
        $quote_ids = [];
        try {
            $inquiry_items_ids = explode(",", $params['inquiry_item_ids']);
            $inquiry_items_ids = array_values(array_filter($inquiry_items_ids));

            $select_list = QuoteSelectedModel::getByInquiryItemIds($inquiry_items_ids);
            $inquiry_item_select_quote_map = [];
            if ($select_list) {
                $quote_ids = array_column($select_list, 'quote_id');
                $quote_list = QuoteModel::getListByIds($quote_ids);
                foreach ($quote_list as &$quote) {
                    $inquiry_item_select_quote_map[$quote['inquiry_items_id']] = $quote;
                }
            }

            $bind_order_list = [];
            $cms_user_info = CmsUserInfoModel::getUserInfoById($params['create_uid']);
            foreach ($inquiry_items_ids as $inquiry_items_id) {
                $quote_id = isset($inquiry_item_select_quote_map[$inquiry_items_id]) ? $inquiry_item_select_quote_map[$inquiry_items_id]['id'] : 0;
                $bind_order_list[] = [
                    "inquiry_item_id" => $inquiry_items_id,
                    "quote_id" => $quote_id,
                    "order_id" => $params['order_id'],
                    "order_sn" => $params['order_sn'],
                    "create_uid" => $params['create_uid'],
                    "create_name" => $cms_user_info['name'],
                    "create_time" => time(),
                ];
            }
            InquiryToOrderModel::batchBindInfos($bind_order_list);
        } catch (\Exception $e) {
            Log::error('bindSaleOrder: ' . json_encode(ErrMsg::getExceptionInfo($e)));
            DB::connection('rfq')->rollBack();
            return $this->setError('绑定关系失败');
        }
        DB::connection('rfq')->commit();


        // 绑定销售单的时候，如果有绑定的报价，那么给采购发送消息
        if ($quote_ids) {
            $RabbitQueueModel = new RabbitQueueModel();
            $sys_msg_data = [
                "event_name" => UserSysMsgService::EVENT_TYPE_QUOTE_BIND_ORDER,
                "event_data" => [
                    "quote_ids" => implode(",", $quote_ids),
                    "order_id" => $params['order_id'],
                ],
            ];
            $RabbitQueueModel->insertQueue('/queue/msg/addMessage', $sys_msg_data,
                $RabbitQueueModel::QUEUE_FRQ_MSG);
        }

        return $this->setSuccess("绑定成功");
    }

    // 来源于询报价系统的销售订单已完成
    public function finishSaleOrder(Request $request)
    {
        $params = [
            'order_id' => $request->input("order_id"),
        ];

        $validator_msg = [
            'order_id.required' => '订单id必填',
        ];
        $validator = Validator::make($params, [
            'order_id' => "required",
        ], $validator_msg);

        if ($validator->fails()) {
            $errors = $validator->errors()->first();
            return $this->setError($errors);
        }

        $inquiry_to_order_list = InquiryToOrderModel::getInquiryBindOrderListByOrderId($params['order_id']);
        if ($inquiry_to_order_list) {
            $order_info = OrderModel::getOrderInfo($params['order_id']);
            if ($order_info) {
                // 订单完成，需要给销售员发送消息
                $RabbitQueueModel = new RabbitQueueModel();
                $sale_uids = array_values(array_unique(array_column($inquiry_to_order_list, 'create_uid')));
                $sys_msg_data = [
                    "event_name" => UserSysMsgService::EVENT_TYPE_ORDER_FINISH,
                    "event_data" => [
                        "sale_uids" => implode(",", $sale_uids),
                        "order_sn" => $order_info['sale_order_sn'],
                        "order_amount" => $order_info['order_amount'],
                    ],
                ];
                $RabbitQueueModel->insertQueue('/queue/msg/addMessage', $sys_msg_data,
                    $RabbitQueueModel::QUEUE_FRQ_MSG);
            }
        }
        return $this->setSuccess("finish");
    }

    private function buildSearchWhere($params, $roleList)
    {
        $where = [];
        //查看自己或下级
        if (!PermService::hasRole(PermService::ROLE_ADMIN, $roleList)) {
            if (PermService::hasRole(PermService::ROLE_SALE_LEADER, $roleList)) {
                $subSaleId = PermService::getSubUserId(\request()->user->userId);
                $where[] = [
                    function ($query) use ($subSaleId) {
                        $query->whereIn('create_uid', $subSaleId);
                    },
                ];
            } else {
                $where[] = [
                    'create_uid',
                    '=',
                    \request()->user->userId,
                ];
            }
        }

        // 客户
        if (isset($params['client_name']) && $params['client_name']) {
            $where[] = [
                'client_name',
                '=',
                $params['client_name'],
            ];
        }
        // 查询销售员
        if (isset($params['create_name']) && $params['create_name']) {
            $where[] = [
                'create_name',
                '=',
                $params['create_name'],
            ];
        }
        // 查询状态
        if (isset($params['o_status']) && is_numeric($params['o_status'])) {
            $where[] = [
                'o_status',
                '=',
                $params['o_status'],
            ];
        }
        if (isset($params['search_date']) && $params['search_date']) {
            [$start_time, $end_time] = explode('~', $params['search_date']);
            $where[] = [
                'create_time',
                '>=',
                strtotime($start_time),
            ];
            $where[] = [
                'create_time',
                '<=',
                strtotime($end_time) + 86399,
            ];
        }
        return $where;
    }
}
