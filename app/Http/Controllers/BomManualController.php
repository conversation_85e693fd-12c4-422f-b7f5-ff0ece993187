<?php

namespace App\Http\Controllers;

use App\Http\Models\BomManualModel;
use App\Http\Utils\NameConvert;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\App;

class BomManualController extends Controller
{
    /**
     * BOM人工处理列表页面
     */
    public function bomManualList(Request $request)
    {
        return view('bomManual.bomManualList');
    }

    /**
     * 获取BOM人工处理列表
     */
    public function getBomManualList(Request $request)
    {
        $params = [
            'page' => $request->input("page", 1),
            'limit' => $request->input("limit", 10),
            'search_date' => $request->input("search_date"),
            'status' => $request->input("status"),
            'bom_sn' => $request->input('bom_sn'),
            'bom_title' => $request->input('bom_title'),
            'assigned_user_name' => $request->input('assigned_user_name'),
            'create_name' => $request->input('create_name'),
        ];

        $validator = Validator::make($params, [
            'page' => 'required|numeric',
            'limit' => 'required|numeric',
            'status' => 'nullable|numeric',
        ]);

        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        $where = $this->buildSearchWhere($params);
        $bom_list = BomManualModel::getListByWhere($where, $params['page'], $params['limit']);

        if ($bom_list && $bom_list['data']) {
            foreach ($bom_list['data'] as &$bom) {
                $bom['status_name'] = BomManualModel::getStatusName($bom['status']);
                $bom['create_time'] = NameConvert::getDateTime($bom['create_time']);
                $bom['update_time'] = NameConvert::getDateTime($bom['update_time']);
            }
        }

        $data = [
            "list" => $bom_list['data'],
            "total" => isset($bom_list['total']) ? $bom_list['total'] : 0,
        ];

        return $this->setSuccess($data);
    }

    /**
     * 获取BOM详情
     */
    public function getBomManualInfo(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递BOM ID");
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return $this->setError("BOM记录不存在");
        }

        $bom_info['status_name'] = BomManualModel::getStatusName($bom_info['status']);
        $bom_info['create_time'] = NameConvert::getDateTime($bom_info['create_time']);
        $bom_info['update_time'] = NameConvert::getDateTime($bom_info['update_time']);

        return $this->setSuccess($bom_info);
    }

    /**
     * 添加BOM记录
     */
    public function addBomManual(Request $request)
    {
        $params = [
            'bom_sn' => $request->input("bom_sn"),
            'bom_title' => $request->input("bom_title"),
            'bom_remark' => $request->input("bom_remark", ''),
            'assigned_user_id' => $request->input("assigned_user_id", 0),
            'assigned_user_name' => $request->input("assigned_user_name", ''),
            'source_file_path' => $request->input("source_file_path", ''),
            'status' => $request->input("status", BomManualModel::STATUS_UNMATCHED),
        ];

        $validator = Validator::make($params, [
            'bom_sn' => 'required|string|max:32',
            'bom_title' => 'required|string|max:255',
            'bom_remark' => 'nullable|string|max:512',
            'assigned_user_id' => 'nullable|integer',
            'assigned_user_name' => 'nullable|string|max:64',
            'source_file_path' => 'nullable|string|max:255',
            'status' => 'nullable|integer|in:1,2,3',
        ]);

        if ($validator->fails()) {
            return $this->setError($validator->errors()->first());
        }

        // 检查BOM编号是否已存在
        if (BomManualModel::existsBomSn($params['bom_sn'])) {
            return $this->setError("BOM编号已存在");
        }

        // 添加创建人信息
        $params['create_uid'] = $request->user->userId;
        $params['create_name'] = $request->user->name ?? '';
        $params['update_uid'] = $request->user->userId;
        $params['update_name'] = $request->user->name ?? '';

        $id = BomManualModel::addRecord($params);

        if ($id) {
            return $this->setSuccess(['id' => $id], "添加成功");
        } else {
            return $this->setError("添加失败");
        }
    }

    /**
     * 更新BOM记录
     */
    public function updateBomManual(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递BOM ID");
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return $this->setError("BOM记录不存在");
        }

        $params = [
            'bom_title' => $request->input("bom_title"),
            'bom_remark' => $request->input("bom_remark"),
            'assigned_user_id' => $request->input("assigned_user_id"),
            'assigned_user_name' => $request->input("assigned_user_name"),
            'source_file_path' => $request->input("source_file_path"),
            'status' => $request->input("status"),
        ];

        // 过滤空值
        $update_data = array_filter($params, function($value) {
            return $value !== null && $value !== '';
        });

        if (empty($update_data)) {
            return $this->setError("没有需要更新的数据");
        }

        // 添加更新人信息
        $update_data['update_uid'] = $request->user->userId;
        $update_data['update_name'] = $request->user->name ?? '';

        $result = BomManualModel::updateById($id, $update_data);

        if ($result) {
            return $this->setSuccess([], "更新成功");
        } else {
            return $this->setError("更新失败");
        }
    }

    /**
     * 删除BOM记录（软删除）
     */
    public function deleteBomManual(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递BOM ID");
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return $this->setError("BOM记录不存在");
        }

        $result = BomManualModel::softDeleteById(
            $id,
            $request->user->userId,
            $request->user->name ?? ''
        );

        if ($result) {
            return $this->setSuccess([], "删除成功");
        } else {
            return $this->setError("删除失败");
        }
    }

    /**
     * BOM详情页面
     */
    public function detail(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return redirect()->back()->with('error', '参数错误');
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return redirect()->back()->with('error', 'BOM记录不存在');
        }

        $bom_info['status_name'] = BomManualModel::getStatusName($bom_info['status']);
        $bom_info['create_time'] = NameConvert::getDateTime($bom_info['create_time']);
        $bom_info['update_time'] = NameConvert::getDateTime($bom_info['update_time']);

        return view('bomManual.detail', ['bom_info' => $bom_info]);
    }

    /**
     * BOM编辑页面
     */
    public function edit(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return redirect()->back()->with('error', '参数错误');
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return redirect()->back()->with('error', 'BOM记录不存在');
        }

        return view('bomManual.edit', ['bom_info' => $bom_info]);
    }

    /**
     * 导出PDF
     */
    public function bomManualPdf(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递BOM ID");
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return $this->setError("BOM记录不存在");
        }

        $bom_info['status_name'] = BomManualModel::getStatusName($bom_info['status']);
        $bom_info['create_time'] = NameConvert::getDateTime($bom_info['create_time']);
        $bom_info['update_time'] = NameConvert::getDateTime($bom_info['update_time']);

        $data = ['bom_info' => $bom_info];
        $html = view('bomManual.bomManualPdf', $data)->render();

        if ($request->input('debug') == 1) {
            return $html;
        }

        $pdf = App::make('dompdf.wrapper');
        $name = 'bom_manual_' . time() . '.pdf';
        $pdf->loadHTML($html)
            ->setPaper('a4', 'portrait')
            ->setWarnings(true);
        return $pdf->stream($name);
    }

    /**
     * 导出Excel
     */
    public function bomManualExcel(Request $request)
    {
        $id = $request->input('id');
        if (empty($id)) {
            return $this->setError("请传递BOM ID");
        }

        $bom_info = BomManualModel::getById($id);
        if (empty($bom_info)) {
            return $this->setError("BOM记录不存在");
        }

        // 这里可以实现Excel导出逻辑
        // 暂时返回成功信息
        return $this->setSuccess([], "Excel导出功能待实现");
    }

    /**
     * 构建搜索条件
     */
    private function buildSearchWhere($params)
    {
        $where = [];

        // BOM编号搜索
        if (!empty($params['bom_sn'])) {
            $where[] = ['bom_sn', 'like', '%' . $params['bom_sn'] . '%'];
        }

        // BOM名称搜索
        if (!empty($params['bom_title'])) {
            $where[] = ['bom_title', 'like', '%' . $params['bom_title'] . '%'];
        }

        // 指定采购员搜索
        if (!empty($params['assigned_user_name'])) {
            $where[] = ['assigned_user_name', 'like', '%' . $params['assigned_user_name'] . '%'];
        }

        // 创建人搜索
        if (!empty($params['create_name'])) {
            $where[] = ['create_name', 'like', '%' . $params['create_name'] . '%'];
        }

        // 状态搜索
        if (!empty($params['status'])) {
            $where[] = ['status', '=', $params['status']];
        }

        // 时间范围搜索
        if (!empty($params['search_date'])) {
            $dates = explode('~', $params['search_date']);
            if (count($dates) == 2) {
                $start_time = strtotime(trim($dates[0]));
                $end_time = strtotime(trim($dates[1]) . ' 23:59:59');
                $where[] = ['create_time', '>=', $start_time];
                $where[] = ['create_time', '<=', $end_time];
            }
        }

        return $where;
    }
}
