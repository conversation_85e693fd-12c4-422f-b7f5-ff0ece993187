<?php

namespace App\Http\Controllers;

use Exception;
use App\Jobs\MatchSku;
use App\Http\Utils\ErrMsg;
use Illuminate\Http\Request;
use App\Http\Models\BomModel;
use App\Http\Caches\BomSnCache;
use App\Http\Models\QuoteModel;
use App\Http\Utils\NameConvert;
use App\Http\Services\BomService;
use App\Http\Models\BomItemsModel;
use Illuminate\Support\Facades\DB;
use App\Http\Services\PriceService;
use Illuminate\Support\Facades\Log;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Models\InquiryModel;
use Maatwebsite\Excel\Facades\Excel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Services\InquiryService;
use App\Http\Services\MessageService;
use App\Http\Services\SetDataService;
use App\Http\Services\BomMatchService;
use App\Http\Services\CustomerService;
use App\Http\Services\SupplierService;
use App\Http\Models\QuoteSelectedModel;
use App\Http\Models\InquiryRelaBomModel;
use App\Http\Services\UserSysMsgService;
use Illuminate\Support\Facades\Validator;
use App\Http\Caches\SaleRecentPointBuyerCache;
use App\Http\Services\SkuService;

class BomController extends Controller
{

    //BOM列表
    public function bomBoard(Request $request)
    {
        $userInfo = CmsUserInfoModel::getUserInfoById($request->user->userId);
        $is_online_saler = in_array($userInfo['department_id'], config('website.online_sale_department_ids')) ? 1 : 0;
        return view('bom.bomBoard', ['is_online_saler' => $is_online_saler]);
    }

    //新增BOM
    public function createBom(Request $request)
    {
        $SaleRecentPointBuyerCache = new SaleRecentPointBuyerCache();
        $recent_buyer_list = $SaleRecentPointBuyerCache->getRecentBuyerNames($request->user->userId);
        $supplier_list_for_xm_select = SupplierService::getSupplierLisForXmSelect(false);
        return view('bom.createBom', [
            "supplier_list_for_xm_select" => $supplier_list_for_xm_select,
            "recent_buyer_list" => $recent_buyer_list,
        ]);
    }

    //BOM详情
    public function bomDetail(Request $request)
    {
        $userInfo = CmsUserInfoModel::getUserInfoById($request->user->userId);
        $is_online_saler = in_array($userInfo['department_id'], config('website.online_sale_department_ids')) ? 1 : 0;
        $supplier_list_for_xm_select = SupplierService::getSupplierLisForXmSelect(false);

        return view(
            'bom.bomDetail',
            ['is_online_saler' => $is_online_saler, 'supplier_list_for_xm_select' => $supplier_list_for_xm_select]
        );
    }

    //修改BOM详情
    public function changeBomDetail(Request $request)
    {
        $SaleRecentPointBuyerCache = new SaleRecentPointBuyerCache();
        $recent_buyer_list = $SaleRecentPointBuyerCache->getRecentBuyerNames($request->user->userId);

        return view('bom.changeBomDetail', [
            "recent_buyer_list" => $recent_buyer_list,
        ]);
    }

    public function getBomList(Request $request)
    {

        try {
            $searchKey = [
                'create_time',
                'bom_status',
                'create_name',
                'bom_sn',
                'bom_title',
                'customer_user_name',
                'bom_with_goods_name',
                'bom_with_brand_name',
                'limit',
                'id',
                'only_self'
            ];
            $returnData = (new BomService())->getBomList($request->only($searchKey));
        } catch (\Exception $exception) {
            return $this->setError($exception->getMessage());
        }

        return $this->setSuccess(SetDataService::groupData($returnData));
    }

    public function getBomDetailList(Request $request)
    {
        try {
            $requestData = $request->only(['id','bom_id', 'limit', 'is_bom_match', 'match_status']);
            $returnData = (new BomService())->getBomDetailList($requestData);
        } catch (\Exception $exception) {
            // dd(json_encode(ErrMsg::getExceptionInfo($exception)));
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }
        return $this->setSuccess(SetDataService::groupData($returnData));
    }

    public function getBomStatusNum(Request $request)
    {
        try {
            $returnData = (new BomService())->getBomStatusNum();
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }
        return $this->setSuccess($returnData);
    }

    public function saveBom(Request $request)
    {
        try {
            (new BomService())->saveBom($request->only(['bom_title', 'bom_remark', 'bom_id', 'bom_items_json']));
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }

        return $this->setSuccessData([
            'bom_id' => $request->input('bom_id'),
            'bom_sn' => BomModel::where('id', $request->input('bom_id'))->value('bom_sn'),
        ]);
    }


    public function bomRelaInquiry(Request $request)
    {
        try {
            $returnData = (new BomService())->bomRelaInquiry($request->only(['inquiry_sn_str']));
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }
        return $this->setSuccess(SetDataService::groupData($returnData));
    }


    //获取内部调用的bom详情ID
    public function getOpenBomDetailList(Request $request)
    {
        try {
            $returnData = (new BomService())->getOpenBomDetailList($request->only([
                'bom_id',
                'bom_item_ids_str',
                'limit'
            ]));
        } catch (\Exception $exception) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }
        return $this->setSuccess(SetDataService::groupData($returnData));
    }


    //新增Bom
    public function addBom(Request $request)
    {
        $base_params = [
            'bom_title' => (string)$request->input("bom_title", ""),
            "customer_user_name" => (string)$request->input("customer_user_name"),
            'delivery_place' => $request->input("delivery_place"),
            'delivery_time' => $request->input("delivery_time"),
            'deadline_time' => $request->input("deadline_time"),
            'bom_remark' => $request->input("bom_remark"),
            'bom_items_json' => $request->input("bom_items_json"),
            'bom_status' => $request->input("bom_status"),
            'supplier_ids' => $request->input("supplier_ids"),
            'is_bom_match' => $request->input("is_bom_match"),
        ];

        $validate_msg = [
            'customer_user_name.required' => '询价客户必填',
            'delivery_place.required' => '交货地必填',
            'delivery_place.in' => '交货地值仅为：1,2',
            'bom_items_json.required' => 'BOM明细必传',
            'bom_status.required' => 'bom_status必传',
            'bom_status.in' => 'bom_status值仅为：1,2',
        ];
        $validator = Validator::make($base_params, [
            'customer_user_name' => "required",
            'delivery_place' => "required|in:1,2",
            'bom_items_json' => "required",
            'bom_status' => "required|in:1,2",
        ], $validate_msg);

        if ($validator->fails()) {
            $errors = $validator->errors()->first();
            return $this->changeBomStatusFromQuote($errors);
        }
        // 询价客户，是否CRM维护客户
        $customer_name = trim($base_params['customer_user_name']);
        $format_customer_list = CustomerService::getCustomCompany($request->user->userId, $customer_name);
        // 公司名称
        $crm_customer_name_info_map = array_column($format_customer_list, null, 'com_name');
        // 手机号
        $crm_customer_mobile_info_map = array_column($format_customer_list, null, 'mobile');
        // 邮箱
        $crm_customer_email_info_map = array_column($format_customer_list, null, 'email');

        $crm_customer_info_map = $crm_customer_name_info_map + $crm_customer_mobile_info_map + $crm_customer_email_info_map;

        if (!array_key_exists($customer_name, $crm_customer_info_map)) {
            return $this->setError("您名下未查找到该客户，请确认客户信息或前往CRM系统维护客户信息");
        }

        $bom_items_params = json_decode($base_params['bom_items_json'], true);
        if (!$bom_items_params) {
            return $this->setError("Bom明细至少一条");
        }
        try {
            $bom_items = BomService::handleBomItemsParams($bom_items_params);
        } catch (Exception $exception) {
            return $this->setError($exception->getMessage());
        }
        $bom_id = 0;
        DB::connection('rfq')->beginTransaction();
        try {
            $bom_cache = new BomSnCache();
            $bom_sn = $bom_cache->getBomSn();
            $bom_id = BomModel::addBom([
                'bom_sn' => $bom_sn,
                'bom_title' => $base_params['bom_title'] ?? '',
                'customer_user_name' => $customer_name,
                'user_id' => $crm_customer_info_map[$customer_name]['user_id'] ?? 0,
                'com_id' => $crm_customer_info_map[$customer_name]['com_id'] ?? 0,
                'delivery_place' => $base_params['delivery_place'],
                'delivery_time' => $base_params['delivery_time'] ?? '',
                'deadline_time' => $base_params['deadline_time'] ? strtotime($base_params['deadline_time']) : '',
                'bom_remark' => $base_params['bom_remark'] ?? '',
                'bom_status' => $base_params['bom_status'],
                'create_uid' => $request->user->userId,
                'create_name' => $request->user->name,
                'update_time' => time(),
                'create_time' => time(),
            ]);

            $add_inquiry_bom_items_ids = [];
            foreach ($bom_items as $bom_item) {
                //这里是因为新增的时候,如果只能报价,只能整单,所以要把选择的supplier_ids全部分配到详情里面去;
                //到时候还要根据详情选择一条或者多条一起修改supplier_ids,所以要存到详情里面去
                $bom_item['supplier_ids'] = $base_params['supplier_ids'] ?? '';
                $bom_item['is_bom_match'] = $base_params['is_bom_match'] ?? 0;
                $bom_item['bom_id'] = $bom_id;
                $bom_item['create_uid'] = $request->user->userId;
                $bom_item['create_name'] = $request->user->name;
                $bom_item['update_time'] = time();
                $bom_item['create_time'] = time();
                $bom_items_id = BomItemsModel::addBomItems($bom_item);

                if ($bom_item['is_inquiry'] == BomItemsModel::IS_INQUIRY_YES) {
                    $add_inquiry_bom_items_ids[] = $bom_items_id;
                }
            }
        } catch (Exception $exception) {
            DB::connection('rfq')->rollBack();
            Log::error(json_encode(ErrMsg::getExceptionInfo($exception)));
            return $this->setError($exception->getMessage());
        }

        DB::connection('rfq')->commit();


        // 保存并提交，创建询价
        if ($base_params['bom_status'] == BomModel::STATUS_WAIT_QUOTE) {
            $RabbitQueueModel = new RabbitQueueModel();
            foreach ($add_inquiry_bom_items_ids as $inquiry_bom_items_id) {
                $queue_data = [
                    'bom_item_id' => $inquiry_bom_items_id,
                ];
                $RabbitQueueModel->insertQueue('/queue/bom/addInquiry', $queue_data, RabbitQueueModel::QUEUE_FRQ);
            }

            (new BomService())->changeBomStatusFromSaveBom($bom_id);
        }

        if ($base_params['is_bom_match'] == 1) {
            if (count($bom_items) < 10) {
                (new MatchSku($bom_id))->handle();
            } else {
                //这里要启动异步任务处理匹配
                dispatch(new MatchSku($bom_id));
            }
        }

        return $this->setSuccessData([
            'bom_id' => $bom_id,
            'bom_sn' => $bom_sn,
        ]);
    }

    // Bom新增创建询价单
    public function addInquiry(Request $request)
    {
        if (!$request->has('bom_item_id')) {
            return $this->setError('bom_item_id必传');
        }

        $bom_item_id = $request->bom_item_id;
        $bom_item_info = BomItemsModel::getById($bom_item_id);
        if (!$bom_item_info) {
            return $this->setError('bom明细不存在');
        }

        $bom_info = BomModel::getById($bom_item_info['bom_id']);
        if (!$bom_info) {
            return $this->setError('bom不存在');
        }

        $RabbitQueueModel = new RabbitQueueModel();
        $inquiry_param = [
            'goods_name' => $bom_item_info['goods_name'],
            'brand_name' => $bom_item_info['brand_name'],
            'inquiry_number' => $bom_item_info['inquiry_number'],
            'delivery_place' => $bom_info['delivery_place'],
            'delivery_time' => $bom_item_info['delivery_time'],
            'batch' => $bom_item_info['batch'],
            'assign_name' => $bom_item_info['assign_buyer_names'],
            'user_name' => $bom_info['customer_user_name'],
            'deadline_time' => date("Y-m-d H:i:s", $bom_item_info['deadline_time']),
            'remark' => $bom_item_info['remark'],
            'inquiry_type' => $bom_item_info['inquiry_type'],
            'inquiry_create_user_name' => $bom_item_info['create_name'],
            'inquiry_create_user_id' => $bom_item_info['create_uid'],
            'bom_item_id' => $bom_item_info['id'],
            'demand_type' => InquiryItemModel::DEMAND_TYPE_BOM
        ];
        $RabbitQueueModel->insertQueue('/queue/inquiry/addInquiry', $inquiry_param, RabbitQueueModel::QUEUE_FRQ);

        return $this->setSuccess([]);
    }

    // bom重新匹配询价单
    public function reInquiry(Request $request)
    {
        if (!$request->has('bom_item_id')) {
            return $this->setError('bom_item_id必传');
        }

        $bom_item_id = $request->bom_item_id;
        $bom_item_info = BomItemsModel::getById($bom_item_id);
        if (!$bom_item_info) {
            return $this->setError('bom明细不存在');
        }

        // 查询bom关联询价明细，询价重新匹配报价，如果未关联询价，那么不能重新匹配
        $bom_rela_inquiry_info = InquiryRelaBomModel::getBomRelaInfoByBomItemId($bom_item_id);
        if (empty($bom_rela_inquiry_info)) {
            return $this->setError("bom未关联询价单，不能重新询价,bom_item_id:{$bom_item_id}");
        }

        $RabbitQueueModel = new RabbitQueueModel();
        $RabbitQueueModel->insertQueue('/queue/quote/autoQuoteByAbility', [
            "inquiry_items_id" => $bom_rela_inquiry_info['inquiry_item_id'],
            'ignore_bom_sku_match' => 1
        ], RabbitQueueModel::QUEUE_FRQ);
        return $this->setSuccess([]);
    }

    //取消BOM
    public function cancelBom(Request $request)
    {
        if (!$request->has('bom_ids')) {
            return $this->setError('bom_ids必传');
        }

        $bom_ids_str = $request->input('bom_ids');
        $bom_ids = explode(',', $bom_ids_str);

        BomModel::updateByIds($bom_ids, [
            'bom_status' => BomModel::STATUS_CANCELED,
            'update_time' => time(),
        ]);

        $extra_where['in_bom_ids'] = $bom_ids;
        $inquiry_rela_list = InquiryRelaBomModel::getItemsByWhere([], $extra_where);
        $inquiry_item_ids = array_column($inquiry_rela_list, 'inquiry_item_id');

        if ($inquiry_item_ids) {
            // 所有未关闭的询价单置为关闭状态。
            $RabbitQueueModel = new RabbitQueueModel();
            $queue_data = [
                'ids' => join(',', $inquiry_item_ids),
            ];
            $RabbitQueueModel->insertQueue('/queue/inquiry/closeInquiry', $queue_data, RabbitQueueModel::QUEUE_FRQ);
        }

        return $this->setSuccess([]);
    }

    //催采购核价
    public function urgeBuyerCheck(Request $request)
    {
        if (!$request->has('bom_id')) {
            return $this->setError('bom_id必传');
        }

        $bom_id = $request->input('bom_id');

        $bom_info = BomModel::getById($bom_id);
        if (!in_array($bom_info['bom_status'], [
            BomModel::STATUS_WAIT_QUOTE,
            BomModel::STATUS_PART_QUOTE,
            BomModel::STATUS_QUOTED,
        ])) {
            return $this->setError('bom状态为待报价，部分报价，已报价才能执行该操作');
        }

        $relation_list = InquiryRelaBomModel::getItemsByWhere([
            ['bom_id', '=', $bom_id],
            ['rela_status', '=', InquiryRelaBomModel::STATUS_ENABLE],
        ]);

        $inquiry_item_ids = array_column($relation_list, 'inquiry_item_id');

        // 选中的报价单中，报价有效时间小于当前时间的报价单对应采购
        $quote_selected_list = QuoteSelectedModel::getByInquiryItemIds($inquiry_item_ids);
        $quote_ids = array_column($quote_selected_list, 'quote_id');

        if ($quote_ids) {
            $extra_where['in_ids'] = $quote_ids;
            $quote_list = QuoteModel::getItemsByWhere([
                ['expire_time', '<', time()],
            ], $extra_where, ['id']);

            $urge_quote_ids = array_column($quote_list, 'id');

            $RabbitQueueModel = new RabbitQueueModel();
            foreach ($urge_quote_ids as $quote_id) {
                $queue_data = [
                    'quote_id' => $quote_id,
                ];
                $RabbitQueueModel->insertQueue('/queue/bom/emailBuyerCheck', $queue_data, RabbitQueueModel::QUEUE_FRQ);
            }
        }

        return $this->setSuccess([]);
    }

    // 催采购核价 - 发邮件
    public function emailBuyerCheck(Request $request)
    {
        if (!$request->has('quote_id')) {
            return $this->setError('quote_id必传');
        }

        $quote_id = $request->input('quote_id');

        $quote_info = QuoteModel::getById($quote_id);

        $quote_user_info = CmsUserInfoModel::getUserInfoByName($quote_info['create_name']);

        $link = config('website.frq_domain') . '/?jumpUrl=/quote/quoteList&quote_sn=' . $quote_info['quote_sn'];
        $link = htmlentities($link);

        $content = "您的报价物料型号：" . $quote_info['goods_name'] . "，品牌：" . $quote_info['brand_name'] . "，报价单号：" . $quote_info['quote_sn'] . " <br><br>报价有效时间已过期，销售 " . $quote_info['inquiry_create_name'] . " 无法在销售订单关联该报价，请尽快确认物料信息并修改报价有效时间，以便销售及时下单。<br><br>点击链接快速跳转询报价系统：" . $link;

        if (isset($quote_user_info['email']) && $quote_user_info['email']) {
            $MessageParam['email'] = $quote_user_info['email'];
            $MessageParam['content'] = $content;

            // 发送邮件
            MessageService::sendMessage($MessageParam, MessageService::KEY_BUYER_CHECK_QUOTE);
        } else {
            Log::error('emailBuyerCheck 无采购邮箱');
        }

        return $this->setSuccess([]);
    }

    /**
     * 展示模板导入的数据结果
     * showImportDataResult
     * @param Request $request
     * <AUTHOR>
     */
    public function showImportDataResult(Request $request)
    {
        $file = $request->file('file');
        if (empty($file)) {
            return $this->setError("请上传文件");
        }
        if (!in_array($file->extension(), ['xlsx', 'bin', 'zip'])) {
            return $this->setError("上传文件格式错误");
        }
        try {
            $array = Excel::toArray(null, $file);
            //去掉表头
            unset($array[0][0]);
            if (empty($array)) {
                throw new \InvalidArgumentException("表格不存在数据");
            }

            $importList = $array[0];
            $importList = array_filter($importList, "App\Http\Utils\ArrUtil::notContainsOnlyNull");
            $importList = array_values($importList);
            // 检查参数是否正常，同时如果有指定采购，附加返回一个指定采购用户信息
            $format_importList = $this->getImportList($importList);
            return $this->setSuccess(["list" => array_values($format_importList)]);
        } catch (\Throwable $throwable) {
            Log::error(json_encode(ErrMsg::getExceptionInfo($throwable)));
            return $this->setError($throwable->getMessage());
        }
    }

    private function getImportList($import_list)
    {
        $excel_import_titles = [
            "goods_name",
            "brand_name",
            "is_inquiry",
            "inquiry_number",
            "delivery_time",
            "batch",
            "assign_buyer_names",
            "deadline_time",
            "remark",
            "inquiry_type",
            "raw_goods_sn",
            "raw_brand_name",
            "customer_material_number",
        ];

        $convert_import_list = [];

        $brand_list = array_column($import_list, 1);
        $standard_brand_map = InquiryService::getBrandNameStandNameMap($brand_list);
        if ($standard_brand_map) {
            foreach ($standard_brand_map as $search_brand => $standard_brand) {
                $standard_brand_map[strtolower($search_brand)] = $standard_brand;
            }
        }

        foreach ($import_list as &$import_item) {
            $import_item = array_slice($import_item, 0, count($excel_import_titles));
            $map_data = array_combine($excel_import_titles, $import_item);

            // 截止时间
            if ($map_data["deadline_time"] && is_numeric($map_data["deadline_time"])) {
                $deadline_time = date("Y-m-d H:i:s", time() + $map_data["deadline_time"] * 3600);
            } else {
                $deadline_time = date("Y-m-d H:i:s", time() + 3 * 86400);
            }

            $brand_name = isset($standard_brand_map[$map_data['brand_name']]) ? $standard_brand_map[$map_data['brand_name']] : "";
            $map_data['goods_name'] = NameConvert::getUppercase(trim($map_data['goods_name']));
            $convert_import_list[] = [
                "goods_name" => ($map_data['goods_name']) ? $map_data['goods_name'] : "",
                "brand_name" => $brand_name,
                "is_inquiry" => ($map_data['is_inquiry'] == "是") ? 1 : 0,
                "inquiry_number" => ($map_data['inquiry_number']) ? $map_data['inquiry_number'] : 0,
                "deadline_time" => $deadline_time,
                "deadline_time_cn" => $deadline_time,
                "delivery_time" => ($map_data['delivery_time']) ? $map_data['delivery_time'] : "",
                "delivery_info" => NameConvert::getDeliveryInfo($map_data['delivery_time']),
                "batch" => ($map_data['batch']) ? $map_data['batch'] : "",
                "assign_buyer_names" => ($map_data['assign_buyer_names']) ? $map_data['assign_buyer_names'] : "",
                "remark" => ($map_data['remark']) ? $map_data['remark'] : "",
                "raw_goods_sn" => ($map_data['raw_goods_sn']) ? $map_data['raw_goods_sn'] : "",
                "raw_brand_name" => ($map_data['raw_brand_name']) ? $map_data['raw_brand_name'] : "",
                "customer_material_number" => ($map_data['customer_material_number']) ? $map_data['customer_material_number'] : "",
            ];
        }
        return $convert_import_list;
    }


    // 获取bom信息
    public function getBomItemListForOrder(Request $request)
    {
        $bom_id = $request->input('bom_id');
        $bom_items_ids = $request->input('bom_items_ids');
        $bom_items_ids = explode(",", $bom_items_ids);
        $bom_items_ids = array_values(array_filter($bom_items_ids));
        if (empty($bom_id) && empty($bom_items_ids)) {
            return $this->setError("请传递bom_id，或bom_items_ids");
        }

        if ($bom_id) {
            $bom_info = BomModel::getById($bom_id);
            $bom_items_list = BomItemsModel::getListByBomId($bom_id);
            if (empty($bom_items_list)) {
                return $this->setError("bom明细查询失败");
            }
        } else {
            $bom_items_list = BomItemsModel::getListByIds($bom_items_ids);
            if (empty($bom_items_list)) {
                return $this->setError("bom明细查询失败");
            }
            $bom_info = BomModel::getById($bom_items_list[0]['bom_id']);
        }

        $goodsInfoList = SkuService::getGoodsInfoByGoodsIds(array_column($bom_items_list, 'goods_id'));
        $rela_inquiry_bom_item_ids = [];
        foreach ($bom_items_list as $bom_item) {
            if ($bom_item['is_inquiry'] == BomItemsModel::IS_INQUIRY_YES) {
                $rela_inquiry_bom_item_ids[] = $bom_item['id'];
            }
        }
        if ($rela_inquiry_bom_item_ids) {
            $inquiry_rela_bom_list = InquiryRelaBomModel::getListByBomItemIds($rela_inquiry_bom_item_ids);
            $bom_item_id_rela_inquiry_item_id_map = array_column(
                $inquiry_rela_bom_list,
                'inquiry_item_id',
                'bom_item_id'
            );
            $inquiry_item_ids = array_column($inquiry_rela_bom_list, 'inquiry_item_id');
            $inquiry_item_select_quote_map = InquiryService::getInquirySelectQuoteMap($inquiry_item_ids);
        }
        $format_bom_item_list = [];
        foreach ($bom_items_list as $bom_item) {
            $currency = ($bom_info['delivery_place'] == BomModel::DELIVERY_PLACE_CN) ? PriceService::CURRENCY_TYPE_RMB : PriceService::CURRENCY_TYPE_US;
            $goodsInfo = $goodsInfoList[$bom_item['goods_id']] ?? [];
            //获取价格
            if ($bom_item['goods_id']) {
                $price = SkuService::getSinglePrice($bom_info['delivery_place'], $bom_item['inquiry_number'], $goodsInfo);
            } else {
                $price = [];
            }
            $format_bom_item = [
                'id' => $bom_item['id'],
                "goods_name" => $bom_item['goods_name'],
                "brand_name" => $bom_item['brand_name'],
                "batch" => $bom_item['batch'],
                "delivery_time" => $bom_item['delivery_time'],
                'currency' => $currency,
                "currency_val" => NameConvert::getCurrencyName($currency),
                "price_origin" => 0,
                "price_rmb" => 0,
                "price_tax" => $price['price'] ?? 0,
                "quote_number" => $bom_item['inquiry_number'],
            ];

            if ($bom_item['is_inquiry'] == BomItemsModel::IS_INQUIRY_YES) {
                $rela_inquiry_item_id = isset($bom_item_id_rela_inquiry_item_id_map[$bom_item['id']]) ? $bom_item_id_rela_inquiry_item_id_map[$bom_item['id']] : 0;
                $inquiry_select_quote_info = isset($inquiry_item_select_quote_map[$rela_inquiry_item_id]) ? $inquiry_item_select_quote_map[$rela_inquiry_item_id] : [];
                if ($inquiry_select_quote_info) {
                    $price_tax = ($inquiry_select_quote_info['currency'] == PriceService::CURRENCY_TYPE_RMB) ? $inquiry_select_quote_info['price_rmb'] : $inquiry_select_quote_info['price_origin'];
                    $format_bom_item = [
                        'id' => $bom_item['id'],
                        "goods_name" => $inquiry_select_quote_info['goods_name'],
                        "brand_name" => $inquiry_select_quote_info['brand_name'],
                        "batch" => $inquiry_select_quote_info['batch'],
                        "delivery_time" => $inquiry_select_quote_info['delivery_time'],
                        'currency' => $inquiry_select_quote_info['currency'],
                        "currency_val" => NameConvert::getCurrencyName($inquiry_select_quote_info['currency']),
                        "price_origin" => $inquiry_select_quote_info['price_origin'],
                        "price_rmb" => $inquiry_select_quote_info['price_rmb'],
                        "price_tax" => $price_tax,
                        "quote_number" => $inquiry_select_quote_info['quote_number'],
                    ];
                }
            }
            $format_bom_item_list[] = $format_bom_item;
        }

        $data = [
            "list" => array_values($format_bom_item_list),
        ];
        return $this->setSuccess($data);
    }

    // 更新bom_items表的sku_id
    public function updateBomItemsGoodsId(Request $request)
    {
        $bom_items_id = $request->input('bom_items_id');
        $goods_id = $request->input('goods_id');
        if (empty($bom_items_id) || empty($goods_id)) {
            return $this->setError("请同时传递bom_items_id，或goods_id");
        }
        $result = BomMatchService::updateBomItemsGoodsId($bom_items_id, $goods_id);
        if ($result) {
            return $this->setSuccess('更新成功');
        } else {
            return $this->setError("更新失败");
        }
    }

    // 重新匹配多个bom详情;
    public function batchBomItems(Request $request)
    {
        $bom_items_ids = $request->input('bom_items_ids');
        $bom_items_ids = explode(",", $bom_items_ids);
        $bom_items_ids = array_values(array_filter($bom_items_ids));
        $supplier_ids = $request->input('supplier_ids');
        $supplier_ids = explode(",", $supplier_ids);
        $supplier_ids = array_values(array_filter($supplier_ids));
        if (empty($bom_items_ids)) {
            return $this->setError("请传递bom_items_ids");
        }
        if (count($bom_items_ids) <= 10) {
            (new MatchSku(0, $bom_items_ids, $supplier_ids))->handle();
        }else{
            dispatch(new MatchSku(0, $bom_items_ids, $supplier_ids));
        }
        return $this->setSuccess('重新匹配请求发送成功,请等待匹配完成');
    }

    //设置选择智能询价
    public function updateSelectBomMatch(Request $request)
    {
        $bom_items_id = $request->input('bom_items_id');
        $select_bom_match = $request->input('select_bom_match');

        $result = BomMatchService::updateSelectBomMatch($bom_items_id, $select_bom_match);
        if ($result) {
            return $this->setSuccess('设置成功');
        } else {
            return $this->setError("设置失败");
        }
    }

    // 批量重新生成询报价
    public function batchReInquiry(Request $request)
    {
        $bom_items_ids = $request->input('bom_items_ids');
        if (empty($bom_items_ids)) {
            return $this->setError("请传递bom_items_ids");
        }
        if (is_string($bom_items_ids)) {
            $bom_items_ids = explode(",", $bom_items_ids);
            $bom_items_ids = array_values(array_filter($bom_items_ids));
        }
        if (empty($bom_items_ids)) {
            return $this->setError("请传递bom_items_ids");
        }

        $rabbit_queue_model = new RabbitQueueModel();
        foreach ($bom_items_ids as $bom_items_id) {
            $rabbit_queue_model->insertQueue('/queue/bom/reInquiry', ['bom_item_id' => $bom_items_id], RabbitQueueModel::QUEUE_FRQ);
        }
        return $this->setSuccess('生成询报价请求发送成功,请等待生成完成');
    }

    /**
     * 导出BOM详细信息
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function exportBomDetail(Request $request)
    {
        $bom_id = $request->input('bom_id');
        $bom_items_ids = $request->input('bom_items_ids');

        if (empty($bom_items_ids) && empty($bom_id)) {
            return $this->setError("请传递bom_items_ids，或bom_id");
        }

        return BomMatchService::exportBom($bom_id, $bom_items_ids);
    }



}
