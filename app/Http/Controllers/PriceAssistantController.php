<?php

namespace App\Http\Controllers;

use App\Http\Services\PriceAssistantService;
use App\Http\Utils\ValidatorMsg;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PriceAssistantController extends Controller
{

    // 获取商品附加费用
    public function getGoodsExtraCharge(Request $request)
    {
        $params = [
            'goods_name' => $request->input("goods_name"),
            'brand_name' => $request->input("brand_name", ""),
            'goods_number' => $request->input("goods_number", 1),
            'delivery_place' => $request->input("delivery_place", 1),
            'goods_price' => $request->input("goods_price", 0),
            'channel_id' => $request->input("channel_id"),
        ];
        $validator = Validator::make($params, [
            'goods_name' => 'required',
//            'brand_name' => 'required',
            'goods_number' => 'required',
            'goods_price' => 'required',
            'channel_id' => 'present',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        $goods_list = [
            [
                "goods_name" => $params['goods_name'],
                "brand_name" => $params["brand_name"],
                "goods_number" => $params["goods_number"],
                "goods_price" => $params["goods_price"],
                "channel_id" => $params["channel_id"],
                "sku_id" => "0",
            ]
        ];

        // 获取附加费
        $extra_fee_types = [
            PriceAssistantService::EXTRA_FEE_TYPE_TAX,
            PriceAssistantService::EXTRA_FEE_TYPE_LAND,
            PriceAssistantService::EXTRA_FEE_TYPE_EQUIVALENT,
            PriceAssistantService::EXTRA_FEE_TYPE_INSPECTION,
            PriceAssistantService::EXTRA_FEE_TYPE_HK_CLEARANCE,
            PriceAssistantService::EXTRA_FEE_TYPE_CHANNEL,
        ];
        $goods_list_with_extra_charge = PriceAssistantService::getGoodsListWithExtraFee($goods_list,
            $params['delivery_place'],
            $extra_fee_types);
        $first_extra_charge_info = ($goods_list_with_extra_charge[0]) ? $goods_list_with_extra_charge[0] : [];
        $data = [
            'extra_charge_info' => $first_extra_charge_info,
        ];
        return $this->setSuccess($data);
    }

    // 批量获取商品附加费用
    public function GetMultiGoodsExtraCharge(Request $request)
    {
        $goods_list = $request->input('goods_list');
        $delivery_place = $request->input('delivery_place', 1);
        $extra_fee_types_str = $request->input('extra_fee_types', '1,2,3,4,5,6');
        $extra_fee_types = explode(',', $extra_fee_types_str);
        $extra_fee_types = array_values(array_filter($extra_fee_types));
        $validator = Validator::make(['goods_list' => $goods_list], [
            'goods_list' => 'required|array',
            'goods_list.*.goods_name' => 'required',
//            'goods_list.*.brand_name' => 'required',
            'goods_list.*.goods_number' => 'required',
            'goods_list.*.goods_price' => 'required',
            'goods_list.*.channel_id' => 'required',
            'goods_list.*.sku_id' => 'present',
        ]);
        if ($validator->fails()) {
            $errors = $validator->errors()->toArray();
            $err_msg = ValidatorMsg::getMsg($errors);
            return $this->setError($err_msg);
        }

        // 获取附加费
        $goods_list_with_extra_charge = PriceAssistantService::getGoodsListWithExtraFee($goods_list, $delivery_place,
            $extra_fee_types);
        $data = [
            'list' => $goods_list_with_extra_charge,
        ];

        return $this->setSuccess($data);
    }


}
