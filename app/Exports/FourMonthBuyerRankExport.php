<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

// 设置excel的首行对应的表头信息
use Maatwebsite\Excel\Concerns\WithMapping;

// 设置excel中每列要展示的数据

class FourMonthBuyerRankExport implements FromCollection, WithHeadings, WithMapping
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return collect($this->data);
    }

    /**
     * 指定excel中每一列的数据字段
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row['user_name'],
            $row['department'],
            (string)($row['to_purchase_num'] ?? '0'),
            (string)($row['to_purchase_money'] ?? '0'),
            (string)($row['ability_to_order_num'] ?? '0'),
            (string)($row['ability_to_order_money'] ?? '0'),
            (string)($row['strong_a_ability_to_order_num'] ?? '0'),
            (string)($row['strong_a_ability_to_order_money'] ?? '0'),
            (string)($row['strong_b_ability_to_order_num'] ?? '0'),
            (string)($row['strong_b_ability_to_order_money'] ?? '0'),
            (string)($row['weak_ability_to_order_num'] ?? '0'),
            (string)($row['weak_ability_to_order_money'] ?? '0'),
        ];
    }

    /**
     * 指定excel的表头
     * @return array
     */
    public
    function headings(): array
    {
        return [
            '采购员',
            '部门',
            '询报价转采购单数',
            '询报价转采购单金额',
            'SKU转单数',
            'SKU转单金额',
            '强履约A转单数',
            '强履约A转单金额',
            '强履约B转单数',
            '强履约B转单金额',
            '弱履约转单数',
            '弱履约转单金额',
        ];
    }
}
