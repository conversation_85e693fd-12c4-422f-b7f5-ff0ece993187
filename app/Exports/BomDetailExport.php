<?php

namespace App\Exports;

use App\Http\Models\BomModel;
use App\Http\Models\BomItemsModel;
use App\Http\Services\BomService;
use App\Http\Utils\NameConvert;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BomDetailExport implements FromView, WithColumnWidths, WithEvents
{
    private $bom_items;
    private $bom_info;

    public function __construct($bom_items, $bom_info = [])
    {
        $this->bom_items = $bom_items;
        $this->bom_info = $bom_info;
    }

    public function view(): View
    {
        return view('bom.bomDetailExcel', [
            'bom_items' => $this->bom_items,
            'bom_info' => $this->bom_info,
        ]);
    }

    // 设置列宽
    public function columnWidths(): array
    {
        $columns = [
            'A' => 20,    // BOM单号
            'B' => 20,    // 询价客户
            'C' => 12,    // 交货地
            'D' => 20,    // 询价型号
            'E' => 16,    // 标准品牌
            'F' => 12,    // 数量
            'G' => 16,    // 期待交期
            'H' => 16,    // 期待批次
            'I' => 20,    // 截止时间
            'J' => 16,    // 报价来源
            'K' => 16,    // 渠道
            'L' => 16,    // 采购员
            'M' => 20,    // 报价型号
            'N' => 16,    // 标准品牌
            'O' => 12,    // 数量
            'P' => 16,    // 含税单价
            'Q' => 16,    // 未税单价
            'R' => 16,    // 报价交期
            'S' => 16,    // 报价批次
            'T' => 20,    // 报价备注
            'U' => 20,    // 报价有效时间
        ];

        return $columns;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // 表头单元格字体颜色
                $event->sheet->getDelegate()->getStyle('A1:U1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => [
                            'rgb' => '000000'
                        ]
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'E0E0E0',
                        ],
                    ],
                ]);

                foreach($this->bom_items as $k => $item) {
                    // 长数字格式化
                    $event->sheet->getDelegate()->getStyle('F'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER);
                    $event->sheet->getDelegate()->getStyle('O'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER);
                    $event->sheet->getDelegate()->getStyle('P'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    $event->sheet->getDelegate()->getStyle('Q'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                }
            }
        ];
    }
}
