<?php

namespace App\Exports;

use App\Http\Models\BomModel;
use App\Http\Models\BomItemsModel;
use App\Http\Services\BomService;
use App\Http\Utils\NameConvert;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\WithColumnWidths;
use Maatwebsite\Excel\Concerns\WithEvents;
use Maatwebsite\Excel\Events\AfterSheet;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class BomExport implements FromView, WithColumnWidths, WithEvents
{
    private $bom_items;
    private $bom_info;

    public function __construct($bom_items, $bom_info = [])
    {
        $this->bom_items = $bom_items;
        $this->bom_info = $bom_info;
    }

    public function view(): View
    {
        return view('bom.bomExcel', [
            'bom_items' => $this->bom_items,
            'bom_info' => $this->bom_info,
        ]);
    }

    // 设置列宽
    public function columnWidths(): array
    {
        $columns = [
            'A' => 8,     // 序号
            'B' => 20,    // 型号
            'C' => 16,    // 品牌
            'D' => 12,    // 数量
            'E' => 16,    // 交期
            'F' => 16,    // 询价状态
            'G' => 16,    // 询价单号
            'H' => 16,    // 报价单价
            'I' => 16,    // 总价
            'J' => 16,    // 交货地点
            'K' => 20,    // 最后更新时间
            'L' => 20,    // 客户物料号
        ];

        return $columns;
    }

    public function registerEvents(): array
    {
        return [
            AfterSheet::class => function(AfterSheet $event) {
                // 表头单元格字体颜色
                $event->sheet->getDelegate()->getStyle('A1:L1')->applyFromArray([
                    'font' => [
                        'bold' => true,
                        'color' => [
                            'rgb' => '000000'
                        ]
                    ],
                    'fill' => [
                        'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                        'startColor' => [
                            'rgb' => 'E0E0E0',
                        ],
                    ],
                ]);

                foreach($this->bom_items as $k => $item) {
                    // 长数字格式化
                    $event->sheet->getDelegate()->getStyle('D'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER);
                    $event->sheet->getDelegate()->getStyle('H'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                    $event->sheet->getDelegate()->getStyle('I'.($k+2))->getNumberFormat()->setFormatCode(NumberFormat::FORMAT_NUMBER_00);
                }
            }
        ];
    }
}
