<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

// 设置excel的首行对应的表头信息
use Maatwebsite\Excel\Concerns\WithMapping;

// 设置excel中每列要展示的数据

class SaleInquiryDataExport implements FromCollection, WithHeadings, WithMapping
{
    private $data;

    public function __construct($data)
    {
        $this->data = $data;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return collect($this->data);
    }

    /**
     * 指定excel中每一列的数据字段
     * @param mixed $row
     * @return array
     */
    public function map($row): array
    {
        return [
            $row['department'],
            $row['user_name'],
            (string)($row['inquiry_num'] ?? '0'),
            (string)($row['quoted_inquiry_num'] ?? '0'),
            (string)($row['unquoted_inquiry_num'] ?? '0'),
            $row['quoted_rate'],
            (string)($row['to_order_inquiry_num'] ?? '0'),
            (string)($row['to_order_money'] ?? '0'),
            (string)($row['to_order_gross_money'] ?? '0'),
            $row['to_sale_order_rate'],
            (string)($row['sku_match_inquiry_num'] ?? '0'),
            (string)($row['sku_quoted_num'] ?? '0'),
            $row['sku_match_inquiry_rate'],
            $row['sku_quoted_rate'],
            (string)($row['sku_order_inquiry_num'] ?? '0'),
            $row['sku_order_rate'],
            (string)($row['sku_intime_inquiry_num'] ?? '0'),
            $row['sku_intime_rate_rate'],
            (string)($row['no_goods_inquiry_num'] ?? '0'),
            (string)($row['abnormal_inquiry_num'] ?? '0'),
            $row['average_quote_time']
        ];
    }

    /**
     * 指定excel的表头
     * @return array
     */
    public
    function headings(): array
    {
        return [
            '部门',
            '人员',
            '询价型号数',
            '报价型号数',
            '未报价型号数',
            '报价率',
            '转单数',
            '转单金额（CNY）',
            '转单毛利（CNY）',
            '转单率',
            'SKU匹配型号数',
            'SKU报价型号数',
            'SKU匹配率',
            'SKU报价率',
            'SKU转单型号数',
            'SKU转单率',
            'SKU及时报价型号数',
            'SKU及时报价率',
            '无货型号数',
            '反馈异常数',
            '平均报价时长'
        ];
    }
}
