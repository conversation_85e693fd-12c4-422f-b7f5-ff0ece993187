<?php

namespace App\Console\Commands;

use App\Http\Caches\QuoteNumCache;
use App\Http\Models\Cloud\SupplierQuoteModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FrqUserExtendInfoModel;
use App\Http\Models\FrqUserGoodAtModel;
use App\Http\Models\InquiryItemAssignModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\Order\OrderItemsExtModel;
use App\Http\Models\Pur\PurchaseOrderModel;
use App\Http\Models\QuoteConfigModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\QuoteStatisticsModel;
use App\Http\Queue\RabbitQueueModel;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class ReAllocateInquiry extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 're_allocate_inquiry';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化采购擅长品牌数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        Log::info('re_allocate_inquiry start');

        // 20240103 评级数据  2023.5.1-2023.12.31
        $supp_name_res_map = [];
        for($mon = 8; $mon > 0; $mon--) {
            $start_time = Carbon::now()->subMonths($mon)->startOfMonth()->timestamp;  // 1688227200  5954 1701446400
            $end_time = Carbon::now()->subMonths($mon)->endOfMonth()->timestamp;

            // 4、近半年内，芯链供应商参与报价的次数；    @韩达
            $supplier_quote_list = QuoteModel::where([
                ['create_time', '>=', $start_time],
                ['create_time', '<=', $end_time],
                ['source', '<=', 3],
                ['status', '<=', QuoteModel::STATUS_OFFER],
            ])->get([
                'supplier_name',
                'id'
            ])->toArray();

            $quote_id_supp_name_map = array_column($supplier_quote_list, 'supplier_name', 'id');
            // dd($quote_id_supp_name_map);

            foreach(collect($supplier_quote_list)->groupBy('supplier_name') as $supplier_name => $items) {
                if(!isset($supp_name_res_map[$supplier_name])) {
                    $supp_name_res_map[$supplier_name] = [
                        'quote_num' => $items->count(),
                        'pur_num' => 0,
                        'pur_amount' => 0,
                    ];
                }else{
                    $supp_name_res_map[$supplier_name]['quote_num'] += $items->count();
                }
            }

            // dd($supp_name_res_map);

            $supplier_name_quote_ids = array_column($supplier_quote_list, 'id');

            $order_item_quote_ids = OrderItemsExtModel::whereIn('quote_id', $supplier_name_quote_ids)->where('rec_id', '!=', 0)->get([
                'rec_id','quote_id'
            ])->toArray();

            $quote_id_order_item_id_map = array_unique(array_column($order_item_quote_ids, 'rec_id', 'quote_id'));

            // dd($quote_id_order_item_id_map);

            // 有关联报价单（该报价单的创建来源为自动报价-猎芯专营）、采购单状态为非作废状态的采购单数量。
            $pur_items = DB::connection('pur')->table('lie_purchase_items as pi')
                ->leftJoin('lie_purchase_order as po', "pi.purchase_id", "=", "po.purchase_id")
                ->select([
                    'po.purchase_id',
                    'po.exchange_rate',
                    'pi.order_item_id',
                    'pi.purchase_qty',
                    'pi.price_without_tax',
                ])
                ->whereIn('pi.order_item_id', $quote_id_order_item_id_map)
                ->where('pi.order_item_id', '!=', 0)
                ->where('pi.status', '=', 1)
                // ->where('po.create_time', '>=', $start_time)
                // ->where('po.create_time', '<=', $end_time)
                ->where('po.status', '!=', PurchaseOrderModel::InvalidStatus)
                ->get()->keyBy('order_item_id')->toArray();

            // 成单
            $order_item_id_pur_map = array_column($pur_items, null, 'order_item_id');

            // 5、近半年内，芯链供应商报价成单的数量；    @韩达
            // 6、近半年内，芯链供应商报价成单（采购单）总额；     @韩达

            foreach($quote_id_order_item_id_map as $quote_id => $order_item_id) {
                if(isset($order_item_id_pur_map[$order_item_id])) {
                    $supp_name = $quote_id_supp_name_map[$quote_id] ?? '';
                    $pur = $order_item_id_pur_map[$order_item_id];
                    $pur = json_decode(json_encode($pur), true);

                    $rmb_price = bcmul($pur['price_without_tax'], $pur['exchange_rate'], 6);

                    $supp_name_res_map[$supp_name]['pur_num'] += 1;

                    if(isset($supp_name_res_map[$supp_name])) {
                        $supp_name_res_map[$supp_name]['pur_amount'] += bcmul($rmb_price, $pur['purchase_qty'], 6);
                    } else {
                        $supp_name_res_map[$supp_name]['pur_amount'] = bcmul($rmb_price, $pur['purchase_qty'], 6);
                    }

                }
            }

        }

        //    dd($supp_name_res_map);
        $start_date = Carbon::now()->subMonths(6)->toDateString();
        $end_date = Carbon::now()->subDay()->toDateString();

        $redis = Redis::connection('frq');
        // $redis_key = 'frq_supplier_data_' . date('Ymd', strtotime($start_date)) . '_' . date('Ymd', strtotime($end_date));
        $redis_key = 'frq_supplier_data_20230501_20231231';
        foreach($supp_name_res_map as $supp_name => $res) {
            $redis->hset($redis_key, $supp_name, json_encode($res));
        }

        // // 处理 自动报价设置
        // $ext_list = FrqUserExtendInfoModel::where('auto_quote_setting', '!=', '')->get(['uid', 'auto_quote_setting'])->toArray();
        // $uid_setting_map = array_column($ext_list, 'auto_quote_setting', 'uid');

        // // self::AUTO_QUTE_HISTORY_PUR_ORDER => '历史采购',
        // // self::AUTO_QUTE_LIEXIN_FRANCHISE => '猎芯专营',
        // // self::AUTO_QUTE_BEST_GOODS => '优势货源',
        // foreach($uid_setting_map as $uid => $setting){
        //     $setting_arr = json_decode($setting, true);
        //     QuoteConfigModel::insertFollow([
        //         'uid' => $uid,
        //         'type' => QuoteConfigModel::TYPE_ACTUALS,
        //         'sku' => $setting_arr[1] ? 1 : 0,
        //         'repurchase' => $setting_arr[2] ? 1 : 0,
        //         'history_pur' => $setting_arr[3] ? 1 : 0,
        //         'create_time' => time(),
        //         'update_time' => time(),
        //     ]);
        // }
        // // dd($uid_setting_map);

        echo "used_brands: 处理条数: " . count($supp_name_res_map) . "\n";

        // // 初始化采购擅长品牌数据
        // $user_ext_list = FrqUserExtendInfoModel::where([
        //     ['user_role', '=', FrqUserExtendInfoModel::USER_ROLE_BUYER],
        //     ['update_time', '>=', Carbon::now()->startOfDay()->timestamp],
        // ])->get([
        //     'uid','used_brands'
        // ])->toArray();
        // // dd($user_ext_list);

        // // ["XinaBox","ZEUS","ADI","TDK","MIKE","3M","ABB"]
        // $handle_count = 0;
        // foreach( $user_ext_list as $k=>$v ){
        //     $used_brands = $v['used_brands'] ?? [];
        //     $used_brands_arr = json_decode($used_brands, true);

        //     if( $used_brands_arr ){
        //         $new_brand_data = [];
        //         foreach( $used_brands_arr as $brand ){
        //             $new_brand_data[] = [
        //                 'uid' => $v['uid'],
        //                 'brand_name' => $brand,
        //                 'status' => 1,
        //                 'create_time' => time(),
        //                 'update_time' => time(),
        //             ];
        //         }
        //         FrqUserGoodAtModel::insert($new_brand_data);
        //         echo "used_brands: " . $v['uid'] . " 新增数据条数: " . count($new_brand_data) . "\n";
        //         $handle_count ++;
        //     }

        // }
        // echo "used_brands: 处理条数: " . $handle_count . "\n";

        // // 处理无货报价数据
        // $quote_ids = QuoteModel::where([
        //     ['create_time', '>', Carbon::parse('2023-08-14')->startOfDay()->timestamp],
        //     // ['create_time', '<', Carbon::parse('2023-08-14')->endOfDay()->timestamp],
        //     ['create_time', '<', Carbon::now()->timestamp],
        //     ['remark', '=', '无货'],
        //     ['status', '=', QuoteModel::STATUS_FREE],
        // ])->get([
        //     'id'
        // ])->pluck('id')->toArray();
        // echo 're_allocate_inquiry $update_count : ' . count($quote_ids) . " quote_ids: " . join(',', $quote_ids)  . PHP_EOL;
        // dd(count($quote_ids));

        // $update_count = InquiryItemAssignModel::whereIn('quote_id', $quote_ids)->update([
        //     'close_type' => InquiryItemAssignModel::CLOSE_TYPE_NONE_STOCK,
        //     'update_time' => time()
        // ]);
        // echo 're_allocate_inquiry $update_count : ' . $update_count  . PHP_EOL;

        // $quotes = QuoteModel::where([
        //     ['create_time', '>', Carbon::parse('2023-08-14')->startOfDay()->timestamp],
        //     ['create_time', '<', Carbon::parse('2023-08-14')->endOfDay()->timestamp],
        //     ['remark', '=', '无货'],
        //     ['status', '=', QuoteModel::STATUS_FREE],
        // ])->get([
        //     'id','create_uid'
        // ])->toArray();
        // $group_quotes = collect($quotes)->groupBy('create_uid');
        // $update_data_count = 0;
        // foreach($group_quotes as $create_uid => $group_items){
        //     echo $create_uid . ' ' . count($group_items) .PHP_EOL;
        //     $res = QuoteStatisticsModel::where([
        //         ['user_id', '=', $create_uid],
        //         ['day_val', '=', '2023-08-14'],
        //     ])->update([
        //         'close_none_stock_num' => count($group_items),
        //         'update_time' => time(),
        //     ]);

        //     $update_data_count += $res;
        // }

        // // dd($group_quotes);

        // echo 're_allocate_inquiry $group_quotes : ' . count($group_quotes)  . PHP_EOL;

        // 芯链报价采购 以询报价为准
        // $supp_quotes = SupplierQuoteModel::where([
        //     ['create_time', '>', Carbon::parse('2023-02-01')->timestamp],
        // ])->get(['frq_quote_id', 'create_uid', 'update_time'])->toArray();

        // $quote_ids = array_column($supp_quotes, 'frq_quote_id');
        // $quote_id_supp_quote_map = array_column($supp_quotes, null, 'frq_quote_id');

        // $quotes = QuoteModel::getItemsByWhere([], [
        //     'in_ids' => $quote_ids
        // ], [
        //     'id', 'create_uid'
        // ]);

        // $handle_quote_ids = [];
        // foreach($quotes as $quote){
        //     $supp_quote = $quote_id_supp_quote_map[$quote['id']] ?? [];
        //     if( $supp_quote['create_uid'] != $quote['create_uid'] ){
        //         SupplierQuoteModel::updateByFrqQuoteId($quote['id'], [
        //             'create_uid' => $quote['create_uid'],
        //             'update_time' => time(),
        //         ]);

        //         $handle_quote_ids[] = $quote['id'];
        //     }
        // }

        // echo 're_allocate_inquiry $handle_quote_ids : ' . join(',', $handle_quote_ids)  . PHP_EOL;
        // echo 're_allocate_inquiry end : ' . count($handle_quote_ids)  . PHP_EOL;

        // $day_val_arr = [
        //     "2023-08-08",
        //     "2023-08-09",
        //     "2023-08-10",
        // ];

        // $quote_data = QuoteStatisticsModel::whereIn('day_val', $day_val_arr)->where('department_id', 0)->get(['id','user_id','user_name','department_id','department_name'])->toArray();

        // $all_names = array_column($quote_data, 'user_name');

        // $all_names_users = CmsUserInfoModel::getAllUsersByNames($all_names);
        // $name_info_map = array_column($all_names_users, null, 'name');

        // foreach($quote_data as $to_do_item){
        //     $name_info = $name_info_map[$to_do_item['user_name']] ?? [];

        //     if( $name_info ){
        //         QuoteStatisticsModel::where('id', $to_do_item['id'])->update([
        //             'department_id' => $name_info['department_id'],
        //             'department_name' => $name_info['department_name'],
        //             'user_id' => $name_info['userId'],
        //             'update_time' => time(),
        //         ]);
        //     }
        // }

        // echo 're_allocate_inquiry end : ' . count($quote_data)  . PHP_EOL;
        // $department_list = CmsUserInfoModel::getDepartmentList($all_pur_ids);
        // $user_id_department_map = array_column($department_list, null, 'user_id');

        // $pur_user_id = $pur_quote_data_item['user_id'];
        // if( isset($user_id_department_map[$pur_user_id]) ){
        //     $pur_quote_data_item['department_id'] = $user_id_department_map[$pur_user_id]['department_id'];
        //     $pur_quote_data_item['department_name'] = $user_id_department_map[$pur_user_id]['department'];
        // }else{
        //     $pur_quote_data_item['department_id'] = 0;
        //     $pur_quote_data_item['department_name'] = '';
        // }

        // // 开始 146888  结束146965
        // $all_inquiry_list = InquiryItemModel::where([
        //     ['is_stop_quote','=', 0],
        //     ['inquiry_pattern','!=', 3],
        //     ['id', '>=', 146888],
        //     ['id', '<=', 146965],
        // ])->whereNotIn('status', [
        //     InquiryItemModel::STATUS_CANCEL,
        //     InquiryItemModel::STATUS_CLOSE,
        // ])->get([
        //     'id','goods_name','brand_name'
        // ])->toArray();
        // dd($all_inquiry_list);

        // $all_inquiry_item_ids = array_column($all_inquiry_list, 'id');
        // dd(count($all_inquiry_item_ids));

        // $RabbitQueueModel = new RabbitQueueModel();
        // foreach($all_inquiry_item_ids as $inquiry_items_id){
        //     $RabbitQueueModel->insertQueue('/queue/quote/autoAssignBuyerComplementV2', [
        //         "inquiry_items_id" => $inquiry_items_id,
        //     ], $RabbitQueueModel::QUEUE_FRQ);
        // }

        // echo 're_allocate_inquiry : ' . count($all_inquiry_item_ids) . " | " . join(',', $all_inquiry_item_ids) . PHP_EOL;
        // echo 're_allocate_inquiry end : ' . PHP_EOL;

        // autoAssignBuyerComplementV2

        // $all_inquiry_item_ids = array_column($all_inquiry_list, 'id');
        // $inquiry_item_id_info_map = array_column($all_inquiry_list, null, 'id');

        // $no_quote_inquiry_item_ids = [];

        // $group_inqury_item_ids = array_chunk($all_inquiry_item_ids, 100);
        // foreach($group_inqury_item_ids as $group_item_ids){
        //     $assigned_inquiry_items_ids = QuoteModel::where([
        //         ['quote_type', '=', 0],
        //         ['assign_type', '=', 3],
        //     ])->whereIn('inquiry_items_id', $group_item_ids)
        //     ->groupBy('inquiry_items_id')
        //     ->pluck('inquiry_items_id')
        //     ->toArray();

        //     $no_quote_inquiry_ids = array_diff($group_item_ids, $assigned_inquiry_items_ids);

        //     $no_quote_inquiry_item_ids = array_merge($no_quote_inquiry_item_ids, $no_quote_inquiry_ids);
        // }

        // foreach( $no_quote_inquiry_item_ids as $to_do_inquiry_item_id ){
        //     $inquiry_item_info = $inquiry_item_id_info_map[$to_do_inquiry_item_id] ?? [];
        //     echo "re_allocate_inquiry: " . $to_do_inquiry_item_id . ' ' . $inquiry_item_info['goods_name']  . PHP_EOL;

        //     // $RabbitQueueModel = new RabbitQueueModel();
        //     // $RabbitQueueModel->insertQueue('/inquiry_assign', [
        //     //     "inquiry_items_id" => $to_do_inquiry_item_id,
        //     //     "goods_name" => $inquiry_item_info['goods_name'],
        //     //     "brand_name" => $inquiry_item_info['brand_name'],
        //     //     "assigned_pur_ids" => [],
        //     //     "assigned_pur_quantity" => 3,
        //     //     "probation_type" => 1, // 是否需要补充试用期的，1需要
        //     // ], $RabbitQueueModel::QUEUE_GO_INNER);
        // }
        // echo 're_allocate_inquiry end : ' . count($no_quote_inquiry_item_ids)  . PHP_EOL;
        // Log::info('re_allocate_inquiry end : ' . count($no_quote_inquiry_item_ids));

        return 0;
    }
}
