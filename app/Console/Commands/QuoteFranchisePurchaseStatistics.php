<?php

namespace App\Console\Commands;

use App\Exports\QuotePurInfoExport;
use App\Http\Caches\SkuCache;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\CmsUserIntraCodeModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\InquiryRelaBomModel;
use App\Http\Models\Order\OrderItemsExtModel;
use App\Http\Models\Order\OrderModel;
use App\Http\Models\Pur\PurchaseOrderModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\Supplier\SupplierChannelModel;
use App\Http\Services\PriceService;
use App\Http\Services\SkuService;
use App\Http\Utils\NameConvert;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use Maatwebsite\Excel\Facades\Excel;

class QuoteFranchisePurchaseStatistics extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'quote_franchise_purchase_statistics {type}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'type: today|month';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $type = $this->argument('type');

        $excel_path = "/public/franchise/" . date('Ymd').'.xlsx';

        if( 'today' == $type ){
            // 每天17点 至前一天17点的统计数据
            $start_time = Carbon::yesterday()->addHours(18)->timestamp;
            $end_time = Carbon::today()->addHours(18)->timestamp;
        }elseif( 'month' == $type ){
            // 每天零点生成前一天整月的明细数据，可供链接下载。
            // 例如7/5  00：00生成  7/1   00：00~7/4  23：59的明细表数据；
            // 例如7/1 00：00，生成6/1  00：00~6/30  23：59的数据。
            if( Carbon::now()->toDateString() == Carbon::now()->startOfMonth()->toDateString() ){
                echo 'quote_franchise_purchase_statistics: 每月一号生成 上月明细：' . Carbon::now()->toDateString() . PHP_EOL;
                $start_time = Carbon::now()->subMonth()->startOfMonth()->timestamp;
                $end_time = Carbon::now()->subMonth()->endOfMonth()->endOfDay()->timestamp;
            }else{
                echo 'quote_franchise_purchase_statistics: 每月不是一号 生成当月明细：' . Carbon::now()->toDateString() . PHP_EOL;
                $start_time = Carbon::now()->startOfMonth()->timestamp;
                $end_time = Carbon::yesterday()->endOfDay()->timestamp;
            }

            $excel_path = "/public/franchise/detail_" . date('Ymd', $start_time) . '_' . date('Ymd', $end_time) .'.xlsx';
        }else{
            echo "quote_franchise_purchase_statistics error_type: $type " . PHP_EOL;
            return;
        }
        
        // 1、线上采购已匹配数（不含代购）：$all_quote_num
        // 2、线上采购已匹配未报价数（不含代购）：$wait_quote_num = $all_quote_num - $quote_num
        // 3、线上采购已匹配已报价数：（不含代购）：$quote_num
        // 4、线上采购成单数（不含代购）：$pur_num

        // 1、线上采购已匹配数（不含代购）：即创建时间在昨天17：00到今天17：00的猎芯专营的自动报价报价单数量。
        // 2、线上采购已匹配未报价数（不含代购）：即创建时间在昨天17：00到今天17：00的猎芯专营的自动报价报价单数量，减去第三个：线上采购已匹配已报价数（不含代购）

        // 将1、2、3统计出来的的报价单，对应的采购员名称、采购员编码、部门，EXCEL清单列出来

        // $excel_data[] = ['报价单号','报价状态','采购员名称','采购员编码','部门'];
        // 采购订单编号、询价单号

        // 0704 新增字段
        // 线上采购成单金额（不含代购）：即  线上采购成单的金额总计（未税，转化为人民币合计）。 $pur_amount
        // 线上采购报价率（不含代购）：即  线上采购已匹配已报价数/线上采购已匹配数。 $pur_quote_rate
        // 线上匹配询价型号数（不含代购）：即  线上采购已匹配报价单中的型号，去重后的总数量。 $inquiry_num
 
        // 已释放 "expire_quote_num" => "",
        // 已超时 "release_quote_num" => "",
        // 待确认 "confirming_quote_num" => "",

        $all_quote_num = 0;
        // $wait_quote_num = 0;
        $expire_quote_num = 0;
        $release_quote_num = 0;
        $confirming_quote_num = 0;
        $quote_num = 0;
        $pur_num = 0;
        $order_item_num = 0;
        $order_quote_num = 0;
        $pur_amount = 0;
        $pur_quote_rate = '';
        $inquiry_num = 0;

        $base_quote_list = QuoteModel::getStatisticsItemsByWhere([
            ['create_time', '>=', $start_time],
            ['create_time', '<=', $end_time],
            ['quote_type', '=', QuoteModel::QUOTE_TYPE_FRANCHISE],
        ],[],['id', 'goods_id']);

        // 筛选商品
        // 1、商品状态必须是：上架；
        // 2、商品所属的供应商必须是：非代购；
        // 3、商品所属的供应商性质必须是：非原厂；
        // 4、商品所属的接入方式必须是：非采集；
        // 5、商品对应的供应商编码必须是：非（L0004938、L0007913、L0011546、L0001175、L0007243、L0015040）
        $all_quote_goods_ids = array_unique(array_column($base_quote_list, 'goods_id'));

        $enable_goods_ids = [];
        // 批量查，最好控制一次查询100次 
        $chunks = array_chunk($all_quote_goods_ids, 100);
        foreach($chunks as $chunk_item){
            $enable_goods_ids = array_merge($enable_goods_ids, SkuService::getSkuinfoByGoodsids($chunk_item));
        }
        
        // 所有专营自动报价数
        $all_quote_list = QuoteModel::getStatisticsItemsByWhere([
            ['create_time', '>=', $start_time],
            ['create_time', '<=', $end_time],
            ['quote_type', '=', QuoteModel::QUOTE_TYPE_FRANCHISE],
        ],[
            'in_goods_ids' => $enable_goods_ids ?? [0]
        ],[
            'id','create_uid','inquiry_sn','quote_sn','status','create_name','supplier_name', 'quote_time', 'goods_id', 'create_time', 'inquiry_items_id','goods_name','brand_name','price_origin','goods_stock',
            'currency',
        ]);
        $all_quote_num = count($all_quote_list);

        $status_group = collect($all_quote_list)->groupBy('status');
        $status_count_map = [];
        foreach($status_group as $status => $status_group_items){
            $status_count_map[$status] = count($status_group_items);
        }
        $quote_num = $status_count_map[QuoteModel::STATUS_OFFER] ?? 0;
        $release_quote_num = $status_count_map[QuoteModel::STATUS_FREE] ?? 0;
        $expire_quote_num = $status_count_map[QuoteModel::STATUS_TIME_OUT] ?? 0;
        $confirming_quote_num = ($status_count_map[QuoteModel::STATUS_DRAFT] ?? 0) + ($status_count_map[QuoteModel::STATUS_TO_CONFIRMED] ?? 0);

        // 创建时间在前一天17:00~当天17:00的来源自动报价-猎芯专营的、已报价状态的报价单数；
        $quote_list = QuoteModel::getItemsByWhere([
            ['create_time', '>=', $start_time],
            ['create_time', '<=', $end_time],
            ['quote_type', '=', QuoteModel::QUOTE_TYPE_FRANCHISE],
            ['status', '=', QuoteModel::STATUS_OFFER],
        ],[
            'in_goods_ids' => $enable_goods_ids ?? [0]
        ],['id','create_uid','quote_sn']);
        $quote_num = count($quote_list);
        
        
        // $wait_quote_num = $all_quote_num - $quote_num;

        $inquiry_items_ids = array_unique(array_column($all_quote_list, 'inquiry_items_id'));
        $inquiry_num = count($inquiry_items_ids);

        // 创建时间在前一天17:00~当天17:00的采购单、且有关联报价单（该报价单的创建来源为自动报价-猎芯专营）、采购单状态为非作废状态的采购单数量。
        $pur_items = DB::connection('pur')->table('lie_purchase_items as pi')
            ->leftJoin('lie_purchase_order as po', "pi.purchase_id", "=", "po.purchase_id")
            ->select([
                'po.purchase_id',
                'po.purchase_sn',
                'po.purchase_name',
                'po.exchange_rate',
                'po.currency',
                'po.status',
                'po.supplier_sn',
                'po.supplier_name',
                'pi.purchase_item_id',
                'pi.order_item_id',
                'pi.goods_name',
                'pi.brand_name',
                'pi.purchase_qty',
                'pi.price_in_tax',
                'pi.price_without_tax',
            ])
            ->where('pi.order_item_id', '!=', 0)
            ->where('pi.status', '=', 1)
            ->where('po.create_time', '>=', $start_time)
            ->where('po.create_time', '<=', $end_time)
            ->where('po.status', '!=', PurchaseOrderModel::InvalidStatus)
            ->get();
        
        $finish_list = [];
        $quote_id_pur_sn_map = [];
        $quote_id_pur_info_map = [];

        if($pur_items){
            $pur_items_arr = json_decode(json_encode($pur_items), true);
            $order_item_id_pur_map = array_column($pur_items_arr, null, 'order_item_id');

            $order_item_ids = array_unique(array_column($pur_items_arr, 'order_item_id'));
            $order_item_num = count($order_item_ids);
            if($order_item_ids){
                // $order_item_ext_quote_ids = OrderItemsExtModel::whereIn('rec_id', $order_item_ids)->where('quote_id', '!=', 0)->pluck('quote_id')->toArray();
                $order_item_ext_arr = OrderItemsExtModel::whereIn('rec_id', $order_item_ids)->where('quote_id', '!=', 0)->get([
                    'rec_id','order_id','quote_id','inquiry_sn','quote_sn'
                ])->toArray();

                $order_item_ext_quote_ids = array_column($order_item_ext_arr, 'quote_id');
                $order_quote_num = count($order_item_ext_quote_ids);

                if($order_item_ext_quote_ids){
                    $pur_quote_list = QuoteModel::getItemsByWhere([
                        ['quote_type', '=', QuoteModel::QUOTE_TYPE_FRANCHISE],
                        ['status', '=', QuoteModel::STATUS_OFFER],
                    ],[
                        'in_ids' => $order_item_ext_quote_ids,
                    ],['id', 'goods_id']);

                    if( count($pur_quote_list) > 0 ){
                        $quote_id_ext_map = array_column($order_item_ext_arr, null, 'quote_id');

                        $order_ids = array_column($order_item_ext_arr, 'order_id');
                        $order_arr = OrderModel::whereIn('order_id', $order_ids)->get([
                            'order_id', 'creator_uid'
                        ])->toArray();
                        $order_id_create_uid_map = array_column($order_arr, 'creator_uid', 'order_id');
                        $order_creator = CmsUserInfoModel::whereIn('userId', $order_id_create_uid_map)->get(['userId','name'])->toArray();
                        $order_creator_id_name_map = array_column($order_creator, 'name', 'userId');
                        $order_id_create_name_map = [];
                        foreach($order_id_create_uid_map as $order_id => $uid){
                            $order_id_create_name_map[$order_id] = $order_creator_id_name_map[$uid] ?? 0;
                        }

                        $pur_quote_goods_ids = array_unique(array_column($pur_quote_list, 'goods_id'));
                        $pur_enable_goods_ids = SkuService::getSkuinfoByGoodsids($pur_quote_goods_ids);

                        foreach($pur_quote_list as $quote_item){

                            if( !in_array($quote_item['goods_id'], $pur_enable_goods_ids) ){
                                Log::info('not_pur_enable_goods_ids: ' . $quote_item['goods_id']);
                                continue;
                            }
                            
                            $ext = $quote_id_ext_map[(string)$quote_item['id']];
                            $pur = $order_item_id_pur_map[$ext['rec_id']];

                            // 成单的：采购员，销售员，型号，品牌，采购订单含税金额 
                            $finish_list[] = [
                                'purchase_name' => $pur['purchase_name'],
                                'order_name' => $order_id_create_name_map[$ext['order_id']] ?? '',
                                'goods_name' => $pur['goods_name'],
                                'brand_name' => $pur['brand_name'],
                                'currency' => $pur['currency'],
                                'amount_in_tax' => bcmul($pur['price_in_tax'], $pur['purchase_qty'], 6),
                                'rec_id' => $ext['rec_id'],
                                'order_id' => $ext['order_id'],
                                'purchase_sn' => $pur['purchase_sn'],
                                'inquiry_sn' => $ext['inquiry_sn'],
                                'quote_sn' => $ext['quote_sn'],
                            ];

                            $quote_id_pur_sn_map[$quote_item['id']] = $pur['purchase_sn'];
                            $quote_id_pur_info_map[$quote_item['id']] = $pur;

                            $pur_num ++;

                            // 未税，转化为人民币合计，rmb 的 exchange_rate 默认为 1
                            $rmb_price = bcmul($pur['price_without_tax'], $pur['exchange_rate'], 6);
                            $pur_amount = bcadd($pur_amount, bcmul($rmb_price, $pur['purchase_qty'], 6), 6);
                        }
                        
                    }
                }
            }
        }
        
        $pur_quote_rate = $all_quote_num ? round(bcdiv($quote_num, $all_quote_num, 3) * 100) . '%' : '' ;

        // $res = 'all_quote_num:' . $all_quote_num . ', wait_quote_num:'. $wait_quote_num;
        $res = 'all_quote_num:' . $all_quote_num . ', release_quote_num:'. $release_quote_num . ', expire_quote_num:'. $expire_quote_num . ', confirming_quote_num:'. $confirming_quote_num;
        $res .= ', quote_num:'. $quote_num.', pur_num:'. $pur_num ;
        $res .= ', pur_amount:' . $pur_amount . ', pur_quote_rate:' . $pur_quote_rate . ', inquiry_num:' . $inquiry_num ;
        $res .= ', order_item_num:'.$order_item_num .', order_quote_num:' . $order_quote_num . ', finish_list:'. json_encode($finish_list);

        $redis = Redis::connection('frq');
        $redis->hset('quote_franchise_purchase_statistics', Carbon::now()->toDateString(), $res);

        // 生成excel
        $quote_create_uids = array_unique(array_column($all_quote_list, 'create_uid'));
        $inquiry_items_ids = array_unique(array_column($all_quote_list, 'inquiry_items_id'));

        $inquiry_items = InquiryItemModel::getListByIds($inquiry_items_ids, [
            'id', 'goods_name', 'brand_name', 'inquiry_number', 'create_name', 'create_time', 'create_uid'
        ]);
        $inquiry_item_ids = array_column($inquiry_items, 'id');
        $inquiry_create_uids = array_column($inquiry_items, 'create_uid');
        $inquiry_item_id_info_map = array_column($inquiry_items, null, 'id');

        $bom_inquiry_list = InquiryRelaBomModel::getListByInquiryItemIds($inquiry_item_ids);
        $bom_inquiry_item_ids = array_column($bom_inquiry_list, 'inquiry_item_id');

        $code_list = CmsUserIntraCodeModel::getListByAdminIds($quote_create_uids);
        $admin_code_id_map = array_column($code_list, 'code_id', 'admin_id');

        $user_list = CmsUserInfoModel::getUserInfosByIds($quote_create_uids);
        $uid_depart_name_map = array_column($user_list, 'department_name', 'userId');

        $inquiry_user_list = CmsUserInfoModel::getUserInfosByIds($inquiry_create_uids);
        $inquiry_uid_depart_name_map = array_column($inquiry_user_list, 'department_name', 'userId');

        $supplier_names = array_filter(array_unique(array_column($all_quote_list, 'supplier_name')));
        $supplier_name_code_map = [];
        
        if( $supplier_names ){
            $suppliers = SupplierChannelModel::getSuppliersByWhere([
                ['is_type', '=', SupplierChannelModel::IS_TYPE_REAL],
            ], ['in_supplier_name' => $supplier_names]);
            $supplier_name_code_map = array_column($suppliers, 'supplier_code', 'supplier_name');
        }

        $excel_data[] = [
            '报价单号	',
            '报价状态	',
            '报价渠道	',
            '自动报价来源	',
            '报价时间	',
            '报价时长(m)	',
            
            '采购员名称	',
            '采购员编码	',
            '采购部门	',
            
            '匹配供应商编码	',
            '匹配供应商名称	',
            '是否更改供应商	',
            '匹配型号	',
            '匹配品牌	',
            '匹配库存数量	',
            '匹配价格	',
            '匹配币种	',

            '实报供应商编码	',
            '实报供应商名称	',
            '实报价格	',
            '实报币种	',

            '采购单号	',
            '采购单状态	',
            '采购渠道编码	',
            '采购渠道名称	',
            '实采价格	',
            '实采币种	',

            '询价单号	',
            '询价来源	',

            '询价型号	',
            '询价品牌	',
            '询价数量	',
            '销售员	',
            // '销售部门	',
            '销售组别	',
            '询价时间',
        ];

        foreach($all_quote_list as $quote_item){

            $inquiry_item_info = $inquiry_item_id_info_map[$quote_item['inquiry_items_id']] ?? [];
            
            $pur_info = $quote_id_pur_info_map[$quote_item['id']] ?? [];

            // $excel_data[] = ['报价单号','报价状态','采购员名称','采购员编码','部门'];
            $excel_data[] = [
                $quote_item['quote_sn'],
                QuoteModel::QUOTE_STATUS_MAP[$quote_item['status']] ?? '',
                '自动报价',
                '专营现货',
                $quote_item['quote_time'] ? date("Y-m-d H:i:s", $quote_item['quote_time']) : '',
                $quote_item['quote_time'] ? bcdiv(($quote_item['quote_time'] - $quote_item['create_time']), 60, 0) : 0,
                
                $quote_item['create_name'],
                ( isset($admin_code_id_map[$quote_item['create_uid']]) ) ? $admin_code_id_map[$quote_item['create_uid']] : '',
                ( isset($uid_depart_name_map[$quote_item['create_uid']]) ) ? $uid_depart_name_map[$quote_item['create_uid']] : '',
              
                // '供应商编码','供应商名称','是否更改供应商	','匹配型号','匹配品牌','匹配库存数量','匹配价格',匹配币种
                ( isset($supplier_name_code_map[$quote_item['supplier_name']]) ) ? $supplier_name_code_map[$quote_item['supplier_name']] : '',
                $quote_item['supplier_name'],
                '',
                $quote_item['goods_name'],
                $quote_item['brand_name'],
                $quote_item['goods_stock'],
                $quote_item['price_origin'],
                NameConvert::getCurrencyName($quote_item['currency']),

                // '实报供应商编码','实报供应商名称','实报价格	','实报币种	',
                ( isset($supplier_name_code_map[$quote_item['supplier_name']]) ) ? $supplier_name_code_map[$quote_item['supplier_name']] : '',
                $quote_item['supplier_name'],
                $quote_item['price_origin'],
                NameConvert::getCurrencyName($quote_item['currency']),

                // '采购单号','采购单状态','采购渠道编码','采购渠道名称', 实采价格 实采币种
                isset($quote_id_pur_sn_map[$quote_item['id']]) ? $quote_id_pur_sn_map[$quote_item['id']] : '',
                isset($pur_info['status']) ? (PurchaseOrderModel::STATUS_MAP[$pur_info['status']] ?? '') : '',
                $pur_info['supplier_sn'] ?? '',
                $pur_info['supplier_name'] ?? '',
                $pur_info['price_without_tax'] ?? '',
                $pur_info['currency'] ?? '',

                // '询价单号	','询价来源	',
                $quote_item['inquiry_sn'],
                in_array($quote_item['inquiry_items_id'], $bom_inquiry_item_ids) ? 'BOM' : '非BOM',
                
                // '询价型号','询价品牌','询价数量','销售员','询价时间',
                $inquiry_item_info['goods_name'] ?? '',
                $inquiry_item_info['brand_name'] ?? '',
                $inquiry_item_info['inquiry_number'] ?? '',
                $inquiry_item_info['create_name'] ?? '',

                // 销售部门 销售组别
                $inquiry_uid_depart_name_map[$inquiry_item_info['create_uid']] ?? '',

                $inquiry_item_info['create_time'] ? date('Y-m-d H:i:s', $inquiry_item_info['create_time']) : '',
                
            ];
        }
        
        // $excel_name = '专营自动报价采购信息'.date('Ymd').'.xlsx';
        // $excel_path = "/public/franchise/" . date('Ymd').'.xlsx';
        Excel::store(new QuotePurInfoExport($excel_data), $excel_path);
        
        Log::info('quote_franchise_purchase_statistics: ' . $res);

        return 0;
    }

}
