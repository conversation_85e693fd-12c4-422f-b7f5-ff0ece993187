<?php

namespace App\Console\Commands;

use App\Http\Models\InquiryItemAssignModel;
use App\Http\Models\QuoteModel;
use Illuminate\Console\Command;

class SyncAssignTypeToQuote extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:sync_assign_type_to_quote';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步 lie_inquiry_items_assign 的 assign_type 字段到 lie_quote 表';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $page = 0;
        $limit = 50;

        echo 'start_sync_assign_type_to_quote' . PHP_EOL;

        do {
            $page++;
            $assign_list = InquiryItemAssignModel::getPageList([
                ['quote_id', '!=', 0],
            ], $page, $limit);
            $last_page = $assign_list['last_page'];

            // 类型： 1指定  2领取  3自动分配 4自动报价 5特殊分配规则
            $point_quote_ids = [];
            $take_quote_ids = [];
            $auto_quote_ids = [];
            foreach($assign_list['data'] as $item){
                if( $item['assign_type'] == 1 ){
                    $point_quote_ids[] = $item['quote_id'];
                }
                if( $item['assign_type'] == 2 ){
                    $take_quote_ids[] = $item['quote_id'];
                }
                if( $item['assign_type'] == 3 ){
                    $auto_quote_ids[] = $item['quote_id'];
                }
            }

            QuoteModel::updateByIds(['assign_type' => 1], $point_quote_ids);
            QuoteModel::updateByIds(['assign_type' => 2], $take_quote_ids);
            QuoteModel::updateByWhere(['assign_type' => 3], [
                ['is_auto', '=', 0]
            ], ['in_ids' => $auto_quote_ids]);

            echo 'page: ' . $page . ', total: '. $last_page . PHP_EOL;
        } while ($page < $last_page);

        $auto_num = QuoteModel::updateByWhere(['assign_type' => 4], [
            ['is_auto', '=', 1]
        ]);
        echo 'sync_assign_type_to_quote $auto_num: ' . $auto_num . PHP_EOL;

        echo 'end_sync_assign_type_to_quote' . PHP_EOL;

        return 0;
    }
}
