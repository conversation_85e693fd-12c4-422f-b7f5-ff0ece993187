<?php

namespace App\Console\Commands;

use App\Http\Models\BestGoodsModel;
use Carbon\Carbon;
use Illuminate\Console\Command;

class UpdateExpiredBestGoods extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'update_expired_best_goods {is_first_process=2 : 1-处理所有的；2-10分钟内的}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将过期优势货源更新为无效';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo "start_update_expired_best_goods\n";
        
        $is_first_process = $this->argument('is_first_process');

        // 10分钟检测一次
        if($is_first_process == 2){
            $where = [
                ['g_status', '=', BestGoodsModel::STATUS_ENABLE],
                ['expire_time', '<=', time()],
                ['expire_time', '>=', time() - 600],    
            ];

            $update_data = [
                'g_status' => BestGoodsModel::STATUS_DISABLE,
                'update_time' => time(),
            ];
            $handle_rows = BestGoodsModel::updateByWhere($where, $update_data);
    
        }

        // 初始化处理所有的，无有效期的设置到2023年12月31
        if($is_first_process == 1){
            $where = [
                ['g_status', '=', BestGoodsModel::STATUS_ENABLE],
                ['expire_time', '=', ''],
            ];

            $update_data = [
                'expire_time' => Carbon::parse('2023-12-31')->timestamp,
                'update_time' => time(),
            ];
            $handle_rows = BestGoodsModel::updateByWhere($where, $update_data);
    
        }
        
        echo "expired_rows: " . $handle_rows . PHP_EOL;

        echo "end_update_expired_best_goods\n";
    }
}
