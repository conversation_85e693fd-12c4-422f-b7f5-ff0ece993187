<?php
/**
 * Created by 2023/1/10.
 * User: Jone
 * Info: 2023/1/10
 * Time: 下午4:03
 */

namespace App\Console\Commands;

use App\Http\Models\InquiryItemModel;
use App\Http\Models\Order\OrderItemsExtModel;
use App\Http\Models\Order\OrderModel;
use App\Http\Models\Pur\PurchaseItemsModel;
use App\Http\Models\Pur\PurchaseOrderModel;
use App\Http\Models\QuoteModel;
use App\Http\Models\SearchInquiryStatisticsModel;
use App\Http\Models\SearchLogsModel;
use Illuminate\Console\Command;
use League\CommonMark\Extension\SmartPunct\Quote;

class SearchInquiryStatistics  extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'search_inquiry:statistics';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '搜索询价统计';

    //开始时间和结束时间
    protected $beginTime = 0;
    protected $endTime = 0;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }


    //第一次跑全部的，接下来就跑每天的
    public function handle()
    {
        $today = date('Y-m-d');
        echo '执行开始'.time();

        //统计当天
        $this->beginTime = strtotime($today);
        $this->endTime = $this->beginTime+86400;
        $this->createTodayStatistics();


        //统计全部
//        $todayTime = strtotime($today);
//        $this->beginTime = strtotime('2022-01-01');
//        while ( $this->beginTime < $todayTime){
//
//            $this->endTime = $this->beginTime+86400;
//            $this->createTodayStatistics();
//            $this->beginTime += 86400;
//        }


        echo '执行结束'.time();
    }

    //创建当天的报表
    public function createTodayStatistics()
    {
        $saveData['today_time'] = $this->beginTime;
        $saveData['create_time'] = time();

        //查找搜索里面的所有型号
        $searchGoodsNameData = $this->getSearchGoodsNameData();
        //查找询价里面的所有型号
        $purchaseGoodsNameData = $this->getPurchaseGoodsNameData();

        //结合两者加起来
        $allData = $this->getAllData($searchGoodsNameData,$purchaseGoodsNameData);




        if (!empty($allData)){
            $this->saveData($allData,$saveData);
        }
    }


    public function saveData($baseData,$saveData)
    {
        $searchInquiryStatisticsModel = new SearchInquiryStatisticsModel();

        foreach ($baseData as $value){
            $saveData['goods_name'] = $value['goods_name'];
            $saveData['brand_name'] = $value['brand_name'];
            $saveData['search_num'] = $value['search_num'];

            $inquiryData = $this->getGoodsBrandInquiryNum($value['goods_name'],$value['brand_name']);

            if(empty($inquiryData)){
                $saveData['inquiry_num'] = 0;
                $saveData['success_quote_num'] = 0;
                $saveData['success_quote_rate'] = 0;
                $saveData['fail_quote_num'] = 0;
                $saveData['sale_num'] = 0;
                $saveData['purchase_num'] = 0;
                $saveData['success_order_rate'] = 0;
            }else{

                $inquiryIdArr = array_column($inquiryData,'inquiry_id');

                $saveData['inquiry_num'] = count($inquiryData);
                $successQuoteData = $this->getSuccessQuote($inquiryIdArr);
                $saveData['success_quote_num'] = count($successQuoteData);
                $saveData['success_quote_rate'] = ($saveData['success_quote_num']/$saveData['inquiry_num'])*100;

                $saveData['fail_quote_num'] = $this->getFailQuote($inquiryIdArr);

                $inquiryItemIdArr = array_column($inquiryData,'id');
                $saleData  = $this->getSaleOrderData($inquiryItemIdArr);

                //  销售次数
                $saveData['sale_num']  = count($saleData);

                // 销售单对应的询价单数
                $saveData['sale_inquiry_num']  = count(array_unique(array_column($saleData,'inquiry_item_id')));

                // 采购次数
                if (empty($saleData)){
                    $saveData['purchase_num'] = 0;
                }else{
                    $saveData['purchase_num'] = $this->getPurchaseOrderData(array_column($saleData,'rec_id'));
                }

                $saveData['success_order_rate'] = ($saveData['sale_inquiry_num']/$saveData['inquiry_num'])*100;
            }




            $shiyssId = $searchInquiryStatisticsModel
                ->where('goods_name',$value['goods_name'])
                ->where('brand_name',$value['brand_name'])
                ->where('today_time',$this->beginTime)
                ->value('shiyss_id');

            //有就更新，没有就新增
            if (empty($shiyssId)){
                $searchInquiryStatisticsModel->insertGetId($saveData);
            }else{
                $searchInquiryStatisticsModel->where('shiyss_id',$shiyssId)->update($saveData);
            }
        }

    }

    public function getGoodsBrandInquiryNum($goodsName,$brandName)
    {
        return (new  InquiryItemModel() )
            ->where('create_time','>=',$this->beginTime)
            ->where('create_time','<',$this->endTime)
            ->where('goods_name',$goodsName)
            ->where('brand_name',$brandName)
            ->select('inquiry_id','id')
            ->get()->toArray();
    }


    public function getSuccessQuote($inquiryIdArr)
    {
        return (new  QuoteModel() )
            ->whereIn('inquiry_id',$inquiryIdArr)
            ->whereIn('status',[1])
            ->select('id')
            ->get()->toArray();
    }

    public function getFailQuote($inquiryIdArr)
    {
        return (new  QuoteModel() )
            ->whereIn('inquiry_id',$inquiryIdArr)
            ->whereIn('status',[-1,9])
            ->count('id');
    }




    //获取搜索型号的数据
    public function getSearchGoodsNameData()
    {
        $searchLogsModel = new SearchLogsModel();
        $baseData = $searchLogsModel
            ->where('create_time','>=',$this->beginTime)
            ->where('create_time','<',$this->endTime)
            ->select('goods_name','brand_name')
            ->get()->toArray();

        $returnData = [];

        foreach ($baseData as $key=>$value){

            $goodsBrandKey = $value['goods_name'].$value['brand_name'];

            //设置初始值
            if (!isset($returnData[$goodsBrandKey])){
                $returnData[$goodsBrandKey]['goods_name'] = $value['goods_name'];
                $returnData[$goodsBrandKey]['brand_name'] = $value['brand_name'];
                $returnData[$goodsBrandKey]['search_num'] = 1;
            }else{
                $returnData[$goodsBrandKey]['search_num'] += 1;
            }
        }
        return $returnData;
    }


    //结合两个的型号
    public function getAllData($searchGoodsNameData,$purchaseGoodsNameData)
    {
        $allData = [];
        //搜索里面的所有型号
        if (!empty($searchGoodsNameData)){
            $allData = $searchGoodsNameData;
        }

        //询价里面的所有型号
        if (!empty($purchaseGoodsNameData)){
            foreach ($purchaseGoodsNameData as $key=>$value){
                if (!isset($allData[$key])){
                    $allData[$key] = $value;
                }
            }
        }
        return $allData;
    }

    //获取询价型号的数据
    public function getPurchaseGoodsNameData()
    {
        $searchLogsModel = new InquiryItemModel();
        $baseData = $searchLogsModel
            ->where('create_time','>=',$this->beginTime)
            ->where('create_time','<',$this->endTime)
            ->select('goods_name','brand_name')
            ->get()->toArray();

        $returnData = [];

        foreach ($baseData as $key=>$value){

            $goodsBrandKey = $value['goods_name'].$value['brand_name'];

            //设置初始值
            if (!isset($returnData[$goodsBrandKey])){
                $returnData[$goodsBrandKey]['goods_name'] = $value['goods_name'];
                $returnData[$goodsBrandKey]['brand_name'] = $value['brand_name'];
            }
            $returnData[$goodsBrandKey]['search_num'] = 0;
        }

        return $returnData;
    }


    //获取销售订单数据
    public function getSaleOrderData($inquiryItemIdArr)
    {
        //获取有效的订单id
        $orderIdArr = OrderItemsExtModel::whereIn('inquiry_item_id', $inquiryItemIdArr)->pluck('order_id');
        $orderIdArr = OrderModel::whereIn('order_id', $orderIdArr)->where('sale_order_status','>=',1)->pluck('order_id');
        return OrderItemsExtModel::whereIn('order_id', $orderIdArr)->select('rec_id','inquiry_item_id')->get()->toArray();
    }

    //获取采购数据
    public function getPurchaseOrderData($recIdArr)
    {
        $purchaseIdArr = PurchaseItemsModel::whereIn('order_item_id', $recIdArr)->pluck('purchase_id');
        $purchaseIdArr = PurchaseOrderModel::whereIn('purchase_id', $purchaseIdArr)->where('status','>',-3)->pluck('purchase_id');
        return PurchaseItemsModel::whereIn('purchase_id', $purchaseIdArr)->count('purchase_item_id');
    }





}