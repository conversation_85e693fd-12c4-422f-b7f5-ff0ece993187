<?php

namespace App\Console\Commands;

use App\Http\Models\FrqUserExtendInfoModel;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class UserRestRecover extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'user_rest_recover';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '员工休假状态还原';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 休假员工
        $rest_users = FrqUserExtendInfoModel::getByWhere([
            ['rest_end_time', '>=', Carbon::now()->startOfDay()->timestamp],
            ['rest_end_time', '<=', Carbon::now()->endOfDay()->timestamp],
            ['employee_status', '=', FrqUserExtendInfoModel::EMPLOYEE_STATUS_REST],
        ], [], [
            'id', 'uid', 'employee_status', 'rest_end_time'
        ]);

        $rest_user_uids = array_column($rest_users, 'uid');
        
        $res = FrqUserExtendInfoModel::recoverStatusByWhere([
            ['rest_end_time', '>=', Carbon::now()->startOfDay()->timestamp],
            ['rest_end_time', '<=', Carbon::now()->endOfDay()->timestamp],
            ['employee_status', '=', FrqUserExtendInfoModel::EMPLOYEE_STATUS_REST],
        ]);

        Log::info('员工休假状态还原 : ' . $res . ";" . join(',', $rest_user_uids));

        return 0;
    }
}
