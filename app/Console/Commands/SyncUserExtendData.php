<?php

namespace App\Console\Commands;

use App\Http\Models\CmsUserDepartmentModel;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FeedbackModel;
use App\Http\Models\FrqUserExtendInfoModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\InquiryModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\UserService;
use Carbon\Carbon;
use Illuminate\Console\Command;

class SyncUserExtendData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:sync_user_extend_data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步用户的扩展数据';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo "-----------------开始同步额外数据-----------------\n";
        // 销售
        // $all_department_ids = $next_department_ids = [CmsUserInfoModel::USER_DEPART_TYPE_SALES, CmsUserInfoModel::USER_DEPART_TYPE_BUYER];
        $all_department_ids = $next_department_ids = [CmsUserInfoModel::USER_DEPART_TYPE_SALES];
        while ($next_department_ids) {
            $next_department_ids = CmsUserDepartmentModel::getDepartmentIdsParrentIds($next_department_ids);
            $all_department_ids = array_merge($all_department_ids, $next_department_ids);
        }
        
        $list = CmsUserInfoModel::getSalesBuyerList([], $all_department_ids, 1, 99999);
        echo "销售找到" . count($list['data']) . "条记录\n";
        foreach ($list['data'] as $userInfo) {
            UserService::addUserExtendData($userInfo['userId'], ["status" => $userInfo['status']], FrqUserExtendInfoModel::USER_ROLE_SALE);
            echo "同步数据成功，userId:" . $userInfo['userId'] . "\n";
            //同步报价数
            UserService::incUserExtendNum($userInfo['userId'], 0, 0);
        }

        // 采购
        $all_department_ids = $next_department_ids = [CmsUserInfoModel::USER_DEPART_TYPE_BUYER];
        while ($next_department_ids) {
            $next_department_ids = CmsUserDepartmentModel::getDepartmentIdsParrentIds($next_department_ids);
            $all_department_ids = array_merge($all_department_ids, $next_department_ids);
        }
        
        $list = CmsUserInfoModel::getSalesBuyerList([], $all_department_ids, 1, 99999);
        echo "采购找到" . count($list['data']) . "条记录\n";
        foreach ($list['data'] as $userInfo) {
            UserService::addUserExtendData($userInfo['userId'], ["status" => $userInfo['status']], FrqUserExtendInfoModel::USER_ROLE_BUYER);
            echo "同步数据成功，userId:" . $userInfo['userId'] . "\n";
            //同步报价数
            UserService::incUserExtendNum($userInfo['userId'], 0, 0);
        }

        echo "同步成功\n";
        echo "-----------------结束同步额外数据-----------------\n";


        echo "-----------------处理反馈数据-----------------\n";
        $list = FeedbackModel::where([
            ['handle_status', '=', FeedbackModel::HANDLE_STATUS_ING],
            ['create_time', '<', Carbon::now()->subDay()->timestamp],
        ])->get();
        $fb_list = $list ? $list->toArray() : [];
        
        $RabbitQueueModel = new RabbitQueueModel();
        foreach($fb_list as $fb_info){
            $update_info = [
                'handle_status' => FeedbackModel::HANDLE_STATUS_TRUTH,
                'handle_remark' => '系统处理',
                'update_time' => time(),
            ];
        
            if( $fb_info['sku_id'] ){
                $RabbitQueueModel->insertQueueData([
                    "down_type" => 2,
                    "data"      => [(string)$fb_info['sku_id']]
                ], RabbitQueueModel::QUEUE_FOOTSTONE_BATCH_DOWNSKU);
                echo "反馈数据下架SKU: ".$fb_info['sku_id']." \n";
                $update_info['is_sku_off'] = 1;
            }

            // 采购选中， 扣1分
            if( $fb_info['handle_user_id'] ){
                $update_info['is_sub_score'] = 1;
                echo "反馈数据 采购扣1分 : ".$fb_info['handle_user_id']." \n";
            }

            FeedbackModel::updateFeedBackById($fb_info['fk_id'], $update_info);

            echo "反馈数据处理: ".$fb_info['fk_id']." \n";
        }
        echo "-----------------处理反馈数据结束-----------------\n";
    }
}
