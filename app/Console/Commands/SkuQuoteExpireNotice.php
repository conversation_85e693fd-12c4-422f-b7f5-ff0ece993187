<?php

namespace App\Console\Commands;

use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FrqUserExtendInfoModel;
use App\Http\Models\InquiryItemAssignModel;
use App\Http\Models\QuoteModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\UserService;
use App\Http\Services\UserSysMsgService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SkuQuoteExpireNotice extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sku_quote_expire_notice';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'sku自动报价2小时未处理的超时警告';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     * 
     * 每分钟执行一次
     *
     * @return int
     */
    public function handle()
    {
        echo date("Y-m-d H:i:s") . " start_sku_quote_expire_notice." . "\n";

        // 两个小时前
        $start_time = time() - 60 * 60 * 2;         // 7200s 2h
        $end_time = time() - 60 * 60 * 2 + 60;

        if( 'local' == config('website.env') ){
            $start_time = time() - 60 * 5;
            $end_time = time() - 60 * 5 + 60;
        }

        // 匹配SKU数据成功指派的报价单，未在2小时内报价
        $quote_list = QuoteModel::getItemsByWhere([
            ['create_time', '>=', $start_time],
            ['create_time', '<=', $end_time],
            ['quote_type', '=', QuoteModel::QUOTE_TYPE_FRANCHISE],
            ['status', '=', QuoteModel::STATUS_TO_CONFIRMED],
        ],[],['id', 'create_uid', 'goods_id']);

        $quote_ids = array_column($quote_list, 'id');
        $quote_create_uids = array_column($quote_list, 'create_uid');
        
        echo date("Y-m-d H:i:s") . " notice_quote_ids: " . join(',', $quote_ids) ." \n";

        if( !$quote_ids ){
            echo date("Y-m-d H:i:s") . " end_sku_quote_expire_notice.\n";
            return ;
        }

        // 此报价单（SKU报价二小时）超时未报价时，立即将此渠道此型号的SKU的状态变更为下架
        $quote_goods_ids = array_column($quote_list, 'goods_id');

        foreach ($quote_goods_ids as &$quote_goods_id) {
            $quote_goods_id = (string)$quote_goods_id;
        }

        $RabbitQueueModel = new RabbitQueueModel();
        $RabbitQueueModel->insertQueueData([
            "down_type" => 2,
            "data"      => $quote_goods_ids
        ], RabbitQueueModel::QUEUE_FOOTSTONE_BATCH_DOWNSKU);
        Log::info('专营报价超时下架SKU：'. json_encode($quote_goods_ids));
        
        foreach($quote_ids as $quote_id){
            // 系统内消息， 邮件
            $RabbitQueueModel->insertQueue('/queue/msg/addMessage', [
                'event_name' => UserSysMsgService::EVENT_TYPE_SKU_EXPIRE_QUOTE,
                'event_data' => [
                    'quote_id' => $quote_id,
                ],
            ], RabbitQueueModel::QUEUE_FRQ_MSG);
        }

        // SKU报价单超时3次警告，单个采购员超时警告超过3次
        $over_3_pur_list = FrqUserExtendInfoModel::getByWhere([
            ['sku_quote_expired_times' , '=', 3],
        ], ['in_uids' => $quote_create_uids]);

        $over_3_uids = array_column($over_3_pur_list, 'uid');

        // 采购员，采购主管，采购部门总监
        foreach($over_3_uids as $uid){
            $user_info = CmsUserInfoModel::getUserInfoById($uid);
            if( !$user_info ){
                continue;
            }

            $manager_uids = UserService::getManagerUids($user_info);

            $notice_uids = array_unique(array_merge([$uid], $manager_uids));

            echo date("Y-m-d H:i:s") . "notice_uids: " . join(',', $notice_uids) ." \n";

            foreach($notice_uids as $notice_uid){
                // 系统内消息， 邮件
                $RabbitQueueModel->insertQueue('/queue/msg/addMessage', [
                    'event_name' => UserSysMsgService::EVENT_TYPE_SKU_EXPIRE_QUOTE_THREE_TIMES,
                    'event_data' => [
                        'depart' => $user_info['department_name'] ?? '',
                        'quote_uname' => $user_info['name'] ?? '',
                        'notice_uid' => $notice_uid,
                    ],
                ], RabbitQueueModel::QUEUE_FRQ_MSG);
            }
        }

        echo date("Y-m-d H:i:s") . " end_sku_quote_expire_notice " . join(',', $over_3_uids) ." \n";
        
        return 0;
    }
}
