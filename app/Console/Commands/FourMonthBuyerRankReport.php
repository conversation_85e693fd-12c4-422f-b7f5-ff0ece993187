<?php

namespace App\Console\Commands;

use App\Exports\FourMonthBuyerRankExport;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\Order\OrderItemsExtModel;
use App\Http\Models\Pur\PurchaseOrderModel;
use App\Http\Models\QuoteModel;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;

class FourMonthBuyerRankReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'four_month_buyer_rank_report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '4月份采购业绩导出';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $pur_items = DB::connection('pur')->table('lie_purchase_items as pi')
            ->leftJoin('lie_purchase_order as po', "pi.purchase_id", "=", "po.purchase_id")
            ->select([
                'po.purchase_id',
                'po.purchase_sn',
                'po.purchase_uid',
                'po.purchase_name',
                'po.exchange_rate',
                'po.currency',
                'po.status',
                'po.create_name',
                'pi.purchase_item_id',
                'pi.order_item_id',
                'pi.goods_name',
                'pi.brand_name',
                'pi.purchase_qty',
                'pi.price_in_tax',
                'pi.price_without_tax',
            ])
            ->where('pi.order_item_id', '!=', 0)
            ->where('pi.status', '=', 1)
            ->where('po.purchase_type', '!=', 1)
            ->whereNotIn('po.supplier_id', [6150, 6149, 4426, 3082, 266, 13521, 11042, 3270, 1466, 5966, 1175])
            ->where('po.create_time', '>=', strtotime("2025-04-01 00:00:00"))
            ->where('po.create_time', '<=', strtotime("2025-04-30 23:59:59"))
            ->whereIn('po.status', [
                PurchaseOrderModel::ToAuditStatus,
                PurchaseOrderModel::OngoingStatus,
                PurchaseOrderModel::CompleteStatus
            ])
            ->get()->toArray();

        $buyer_rank_data = [];
        if ($pur_items) {
            $pur_items = array_map('get_object_vars', $pur_items);
            $order_item_ids = array_column($pur_items, 'order_item_id');
            $order_item_ext_infos = OrderItemsExtModel::getExtListByOrderItemIds($order_item_ids);
            $quote_ids = array_column($order_item_ext_infos, 'quote_id');
            $order_item_id_quote_id_map = array_column($order_item_ext_infos, 'quote_id', 'rec_id');
            $quote_list = QuoteModel::select(['id', 'quote_type', 'inquiry_items_id'])->whereIn('id',
                $quote_ids)->get()->toArray();
            $quote_id_quote_type_map = array_column($quote_list, 'quote_type', 'id');
            $quote_id_quote_info_map = array_column($quote_list, null, 'id');
            $inquiry_item_ids = array_column($quote_list, 'inquiry_items_id');
            $inquiry_item_infos = InquiryItemModel::getInquiryItemsByIds($inquiry_item_ids);
            $inquiry_item_id_demand_type_map = array_column($inquiry_item_infos, 'demand_type', 'id');
            $buyer_ids = array_column($pur_items, 'purchase_uid');
            $buyer_list = CmsUserInfoModel::getUserInfosByIds($buyer_ids);
            $buyer_id_info_map = array_column($buyer_list, null, 'userId');
            foreach ($pur_items as $pur_item) {
                $buyer_info = isset($buyer_id_info_map[$pur_item['purchase_uid']]) ? $buyer_id_info_map[$pur_item['purchase_uid']] : [];
                $quote_id = isset($order_item_id_quote_id_map[$pur_item['order_item_id']]) ? $order_item_id_quote_id_map[$pur_item['order_item_id']] : 0;
                $inquiry_item_id = isset($quote_id_quote_info_map[$quote_id]) ? $quote_id_quote_info_map[$quote_id]['inquiry_items_id'] : 0;
                $demand_type = isset($inquiry_item_id_demand_type_map[$inquiry_item_id]) ? $inquiry_item_id_demand_type_map[$inquiry_item_id] : 0;
                $not_bom = ($demand_type !== 5) ? true : false;
                //询价单类型不为BOM询价, 对应上游销售单明细有关联询价单
                if ($buyer_info && $quote_id && $not_bom) {
                    $buyer_name = $buyer_info['name'];
                    if (!isset($buyer_rank_data[$buyer_name])) {
                        $buyer_rank_data[$buyer_name] = self::getUserInitDefaultStatInfo($buyer_info['userId'],
                            $buyer_name, $buyer_info['department_name']);
                    }

                    // 转单数量
                    $buyer_rank_data[$buyer_name]['to_purchase_num']++;
                    // 转单金额
                    $rmb_price = $pur_item['price_in_tax'] * $pur_item['exchange_rate'];
                    $to_purchase_money = bcmul($rmb_price, $pur_item['purchase_qty'], 6);
                    $buyer_rank_data[$buyer_name]['to_purchase_money'] = bcadd($buyer_rank_data[$buyer_name]['to_purchase_money'],
                        $to_purchase_money, 2);

                    // 履约订单
                    $curr_quote_type = isset($quote_id_quote_type_map[$quote_id]) ? $quote_id_quote_type_map[$quote_id] : 0;
                    if (in_array($curr_quote_type, [
                        QuoteModel::QUOTE_TYPE_STRONG_ABILITY,
                        QuoteModel::QUOTE_TYPE_STRONG_B_ABILITY,
                        QuoteModel::QUOTE_TYPE_WEAK_ABILITY
                    ])) {
                        $buyer_rank_data[$buyer_name]['ability_to_order_num']++;
                        $buyer_rank_data[$buyer_name]['ability_to_order_money'] = bcadd($buyer_rank_data[$buyer_name]['ability_to_order_money'],
                            $to_purchase_money, 2);
                    }

                    // 强履约A
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_STRONG_ABILITY) {
                        $buyer_rank_data[$buyer_name]['strong_a_ability_to_order_num']++;
                        $buyer_rank_data[$buyer_name]['strong_a_ability_to_order_money'] = bcadd($buyer_rank_data[$buyer_name]['strong_a_ability_to_order_money'],
                            $to_purchase_money, 2);
                    }
                    // 强履约B
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_STRONG_B_ABILITY) {
                        $buyer_rank_data[$buyer_name]['strong_b_ability_to_order_num']++;
                        $buyer_rank_data[$buyer_name]['strong_b_ability_to_order_money'] = bcadd($buyer_rank_data[$buyer_name]['strong_b_ability_to_order_money'],
                            $to_purchase_money, 2);
                    }
                    // 弱履约A
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_WEAK_ABILITY) {
                        $buyer_rank_data[$buyer_name]['weak_ability_to_order_num']++;
                        $buyer_rank_data[$buyer_name]['weak_ability_to_order_money'] = bcadd($buyer_rank_data[$buyer_name]['weak_ability_to_order_money'],
                            $to_purchase_money, 2);
                    }
                }
            }
        }

        $buyer_rank_list = array_values($buyer_rank_data);
        $collection = collect($buyer_rank_list);
        $collection = $collection->sortByDesc('to_purchase_money');
        $sorted_buyer_rank_list = array_values($collection->all());

        $file_path = "4month_buyer_rank_report" . date("YmdHis") . ".xlsx";
        Excel::store(new FourMonthBuyerRankExport($sorted_buyer_rank_list), $file_path);

        echo "采购导出4月份销售业绩数据完成，file_path:{$file_path}\n";
        return 0;
    }


    // 获取初始化信息
    public static function getUserInitDefaultStatInfo($user_id = 0, $user_name = "", $department = '')
    {
        return [
            'user_id' => $user_id,
            'user_name' => $user_name,
            'department' => $department,
            'to_purchase_num' => 0,
            'to_purchase_money' => 0,
            'ability_to_order_num' => 0,
            'ability_to_order_money' => 0,
            'strong_a_ability_to_order_num' => 0,
            'strong_a_ability_to_order_money' => 0,
            'strong_b_ability_to_order_num' => 0,
            'strong_b_ability_to_order_money' => 0,
            'weak_ability_to_order_num' => 0,
            'weak_ability_to_order_money' => 0,
        ];
    }
}
