<?php

namespace App\Console\Commands;

use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\FrqUserExtendInfoModel;
use App\Http\Models\QuoteHandleStatisticsModel;
use App\Http\Models\QuoteStatisticsModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\UserService;
use App\Http\Services\UserSysMsgService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class QuoteHandleBuyerRanking extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'quote_handle_buyer_ranking';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '对采购进行排序，根据周日均报价数、周日均报价效率，然后发送弹窗消息';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 所有采购
        $all_buyer = FrqUserExtendInfoModel::getByWhere([
            ['user_role', '=', FrqUserExtendInfoModel::USER_ROLE_BUYER],
            ['employee_status', '!=', FrqUserExtendInfoModel::EMPLOYEE_STATUS_QUIT],
        ], ['id','uid']);

        $all_buyer_uids = array_column($all_buyer, 'uid');
        $all_buyer_uids_count = count($all_buyer_uids);
        
        // 鼓励 10%， 警示 20%
        $top_10_num = ceil(bcmul($all_buyer_uids_count, 0.1, 1));
        $last_20_num = ceil(bcmul($all_buyer_uids_count, 0.2, 1));


        // 周日均有效报价
        $buyer_week_quote_data = QuoteStatisticsModel::getByWhereGroupByUserId([
            ['day_val', '>=', Carbon::now()->startOfWeek()->toDateString()],
            // ['day_val', '>=', '2022-10-10'],
        ]);

        $now_weekday_num = Carbon::now()->weekday();
        if( $now_weekday_num == 0 ){
            // 周日 0
            $now_weekday_num = 7;
        }

        $user_id_valid_quote_avg_map = [];
        foreach( $buyer_week_quote_data as $item ){
            $user_id_valid_quote_avg_map[$item['user_id']] = round(bcdiv($item['valid_quote_sum'], $now_weekday_num, 2), 1);
        }
        arsort($user_id_valid_quote_avg_map);

        // 补全未报价的采购id
        foreach($all_buyer_uids as $buyer_id){
            if( !isset($user_id_valid_quote_avg_map[$buyer_id]) ){
                $user_id_valid_quote_avg_map[$buyer_id] = 0;
            }
        }
        
        // 取前10%，后20%
        list($top_10_user_list,) = array_chunk($user_id_valid_quote_avg_map, $top_10_num, true);
       
        asort($user_id_valid_quote_avg_map);
        list($last_20_user_list,) = array_chunk($user_id_valid_quote_avg_map, $last_20_num, true);

        $RabbitQueueModel = new RabbitQueueModel();

        // 末尾
        // 截止目前，您本周的日均有效报价数在倒数第3名，3.4条，与第1名34条还有一点点差距哦，越多报价，越高业绩，加油！
        $first_buyer_quote = $top_10_user_list[array_key_first($top_10_user_list)];
        $last_index = 1;
        foreach( $last_20_user_list as $user_id => $user_quote_avg ){
            $last_content = [
                'content' => "截止目前，您本周的日均有效报价数在倒数第".$last_index."名，".$user_quote_avg."条，与第1名".$first_buyer_quote."条还有一点点差距哦，越多报价，越高业绩，加油！"
            ];
            $last_index++;
            
            // 发送消息
            $queue_data = [
                'user_id' => $user_id,
                'msg_category_id' => UserSysMsgService::MSG_CATE_BUYER_NOTICE,
                'msg_data' => json_encode($last_content, true),
                'to_sys_id' => UserSysMsgService::MSG_SYSTEM_DEFAULT_ID,
                'from_sys_id' => UserSysMsgService::MSG_SYSTEM_FRQ_ID,
            ];
            $RabbitQueueModel->insertQueue('/add_msg', $queue_data, RabbitQueueModel::QUEUE_MSG);

            echo '周日均报价数，警示：' . $user_id . PHP_EOL;
        }

        // 前10%
        // 截止目前，您本周的日均报价数在第5名，平均每天31.5条，超越了72名同事，强的离谱啊！！！
        $top_index = 1;
        foreach( $top_10_user_list as $user_id => $user_quote_avg ){
            $top_content = [
                'content' => "截止目前，您本周的日均报价数在第".$top_index."名，平均每天".$user_quote_avg."条，超越了".($all_buyer_uids_count-$top_index)."名同事，强的离谱啊！！！"
            ];
            $top_index++;
            
            // 发送消息
            $queue_data = [
                'user_id' => $user_id,
                'msg_category_id' => UserSysMsgService::MSG_CATE_BUYER_NOTICE,
                'msg_data' => json_encode($top_content, true),
                'to_sys_id' => UserSysMsgService::MSG_SYSTEM_DEFAULT_ID,
                'from_sys_id' => UserSysMsgService::MSG_SYSTEM_FRQ_ID,
            ];
            $RabbitQueueModel->insertQueue('/add_msg', $queue_data, RabbitQueueModel::QUEUE_MSG);

            echo '周日均报价数，鼓励：' . $user_id . PHP_EOL;
        }

        echo '周日均报价数消息发送完成' . PHP_EOL . PHP_EOL;


        // =============== 报价处理效率
        $quote_handle_list = QuoteHandleStatisticsModel::getCountsByWhereGroupByUerId([
            // ['day_val', '>=', '2022-09-15'],
            ['day_val', '>=', Carbon::now()->startOfWeek()->toDateString()],
        ]);
        
        $handle_quote_ranking_list = [];
        foreach($quote_handle_list as $quote_handle_item){
            $handle_quote_ranking_list[$quote_handle_item['buyer_uid']] = (int)round(bcdiv($quote_handle_item['quote_handle_seconds_avg'], 60, 1));
        }

        if( !$handle_quote_ranking_list ){
            return;
        }

        // 鼓励 10%， 警示 10%
        $all_handle_count = count($handle_quote_ranking_list);
        $_10_handle_num = ceil(bcmul($all_handle_count, 0.1, 1));

        asort($handle_quote_ranking_list);

        // 取前10%，后10%
        list($top_10_arr,) = array_chunk($handle_quote_ranking_list, $_10_handle_num, true);
        $top_handle_mins = $handle_quote_ranking_list[array_key_first($handle_quote_ranking_list)];
        arsort($handle_quote_ranking_list);
        list($last_10_arr,) = array_chunk($handle_quote_ranking_list, $_10_handle_num, true);

        // 鼓励
        // 截止目前，您本周的报价处理效率排名第5，报出一条报价只需要XXX分钟，超越了72名同事，快的离谱啊！！！
        $top_index = 1;
        $top_user_ids = [];
        foreach($top_10_arr as $user_id => $quote_handle_minutes_avg){
            $top_user_ids[] = $user_id;

            $top_content = [
                'content' => "截止目前，您本周的报价处理效率排名第".$top_index."，报出一条报价只需要".$quote_handle_minutes_avg."分钟，超越了".($all_handle_count-$top_index)."名同事，快的离谱啊！！！"
            ];
            $top_index++;
            
            // 发送消息
            $queue_data = [
                'user_id' => $user_id,
                'msg_category_id' => UserSysMsgService::MSG_CATE_BUYER_NOTICE,
                'msg_data' => json_encode($top_content, true),
                'to_sys_id' => UserSysMsgService::MSG_SYSTEM_DEFAULT_ID,
                'from_sys_id' => UserSysMsgService::MSG_SYSTEM_FRQ_ID,
            ];
            $RabbitQueueModel->insertQueue('/add_msg', $queue_data, RabbitQueueModel::QUEUE_MSG);

            echo '周报价处理效率，鼓励：' . $user_id . PHP_EOL;
        }

        // 警示
        // 截止目前，您本周的报价处理效率为倒数15名，平均一条报价处理需要XXX分钟，偷偷告诉你，第一名只需要XXX分钟哦，越快给出报价，越能够提高成单概率，加油！
        $last_index = 1;
        foreach($last_10_arr as $user_id => $quote_handle_minutes_avg){
            if( in_array($user_id, $top_user_ids) ){
                continue;
            }

            $last_content = [
                'content' => "截止目前，您本周的报价处理效率为倒数第".$last_index."名，平均一条报价处理需要".$quote_handle_minutes_avg."分钟，偷偷告诉你，第一名只需要".$top_handle_mins."分钟哦，越快给出报价，越能够提高成单概率，加油！"
            ];
            $last_index++;
            
            // 发送消息
            $queue_data = [
                'user_id' => $user_id,
                'msg_category_id' => UserSysMsgService::MSG_CATE_BUYER_NOTICE,
                'msg_data' => json_encode($last_content, true),
                'to_sys_id' => UserSysMsgService::MSG_SYSTEM_DEFAULT_ID,
                'from_sys_id' => UserSysMsgService::MSG_SYSTEM_FRQ_ID,
            ];
            $RabbitQueueModel->insertQueue('/add_msg', $queue_data, RabbitQueueModel::QUEUE_MSG);

            echo '周报价处理效率，警示：' . $user_id . PHP_EOL;
        }
        
        echo '周报价处理效率消息发送完成' . PHP_EOL;
        
        return 0;
    }
}
