<?php

namespace App\Console\Commands;

use App\Http\Models\InquiryItemModel;
use App\Http\Models\QuoteModel;
use Illuminate\Console\Command;

class ResetInquiryItemStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:reset_inquiry_item_status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $limit = 300;
        $start_id = 0;
        echo date("Y-m-d H:i:s") . "start to reset_inquiry_item_status." . "\n";
        do {
            $inquiry_items_list = InquiryItemModel::listByStartId($start_id, $limit);
            if ($inquiry_items_list && is_array($inquiry_items_list)) {
                $to_ready_item_ids = [];

                foreach ($inquiry_items_list as $inquiry_item) {
                    $quote_list = QuoteModel::getQuotesByInquiryItemsIdAndStatus($inquiry_item['id'],
                        QuoteModel::STATUS_OFFER);
                    if (empty($quote_list)) {
                        // 如果没有该询价单其他的已报价，并且询价单状态为已报价，那么需要恢复为待报价
                        if ($inquiry_item['status'] == InquiryItemModel::STATUS_OFFERED) {
                            $to_ready_item_ids[] = $inquiry_item['id'];
                        }
                    }
                }

                if ($to_ready_item_ids) {
                    InquiryItemModel::updateByIds(['status' => InquiryItemModel::STATUS_READY],
                        ['id' => $to_ready_item_ids]);

                    echo "update status:已报价 to 待报价，inquiry_item_ids:" . json_encode($to_ready_item_ids) . "\n";
                }
                $start_id = $inquiry_items_list[count($inquiry_items_list) - 1]['id'];
            }
        } while (count($inquiry_items_list) >= $limit);
        echo date("Y-m-d H:i:s") . "finish to reset_inquiry_item_status." . "\n";
        return 0;
    }
}
