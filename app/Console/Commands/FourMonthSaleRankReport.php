<?php

namespace App\Console\Commands;

use App\Exports\FourMonthSaleRankExport;
use App\Http\Models\CmsUserInfoModel;
use App\Http\Models\Pur\PurchaseOrderModel;
use App\Http\Models\QuoteModel;
use App\Http\Services\PriceService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Facades\Excel;

class FourMonthSaleRankReport extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'four_month_sale_rank_report';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '4月份销售业绩导出';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {

        $order_items = DB::connection('order')->table('order_items as order_items')
            ->leftJoin('order', "order_items.order_id", "=", "order.order_id")
            ->leftJoin('order_extend', "order_extend.order_id", "=", "order.order_id")
            ->leftJoin('order_items_ext', "order_items_ext.rec_id", "=", "order_items.rec_id")
            ->select([
                'order.sale_id',
                'order_items.order_id',
                'order_items.goods_number',
                'order_items.goods_price',
                'order.currency',
                'order_extend.exchange_rate',
                'order_items.rec_id',
                'order_items_ext.quote_id'
            ])
            ->where('order.create_time', '>=', strtotime("2025-04-01 00:00:00"))
            ->where('order.create_time', '<=', strtotime("2025-04-30 23:59:59"))
            ->whereIn('order.sale_order_status', [2, 3, 4])
            ->where('order_items_ext.quote_id', '!=', 0)
            ->where('order_items_ext.supplier_type', '!=', 1)
            ->get()->toArray();

        $sale_rank_data = [];
        if ($order_items) {
            $order_items = array_map('get_object_vars', $order_items);
            $quote_ids = array_column($order_items, 'quote_id');
            $quote_list = QuoteModel::select(['id', 'quote_type'])->whereIn('id', $quote_ids)->get()->toArray();
            $quote_id_quote_type_map = array_column($quote_list, 'quote_type', 'id');
            $sale_ids = array_column($order_items, 'sale_id');
            $sale_list = CmsUserInfoModel::getUserInfosByIds($sale_ids);
            $sale_id_info_map = array_column($sale_list, null, 'userId');
            foreach ($order_items as $order_item) {
                $sale_info = isset($sale_id_info_map[$order_item['sale_id']]) ? $sale_id_info_map[$order_item['sale_id']] : [];
                if ($sale_info) {
                    $sale_name = $sale_info['name'];
                    if (!isset($sale_rank_data[$sale_name])) {
                        $sale_rank_data[$sale_name] = self::getUserInitDefaultStatInfo($sale_info['userId'],
                            $sale_name, $sale_info['department_name']);
                    }

                    // 转单数量
                    $sale_rank_data[$sale_name]['to_order_num']++;
                    // 转单金额
                    $rmb_price = $order_item['goods_price'] * $order_item['exchange_rate'];
                    $to_order_money = bcmul($rmb_price, $order_item['goods_number'], 6);
                    $sale_rank_data[$sale_name]['to_order_money'] = bcadd($sale_rank_data[$sale_name]['to_order_money'],
                        $to_order_money, 2);

                    // 履约订单
                    $curr_quote_type = isset($quote_id_quote_type_map[$order_item['quote_id']]) ? $quote_id_quote_type_map[$order_item['quote_id']] : 0;
                    if (in_array($curr_quote_type, [
                        QuoteModel::QUOTE_TYPE_STRONG_ABILITY,
                        QuoteModel::QUOTE_TYPE_STRONG_B_ABILITY,
                        QuoteModel::QUOTE_TYPE_WEAK_ABILITY
                    ])) {
                        $sale_rank_data[$sale_name]['ability_to_order_num']++;
                        $sale_rank_data[$sale_name]['ability_to_order_money'] = bcadd($sale_rank_data[$sale_name]['ability_to_order_money'],
                            $to_order_money, 2);
                    }

                    // 强履约A
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_STRONG_ABILITY) {
                        $sale_rank_data[$sale_name]['strong_a_ability_to_order_num']++;
                        $sale_rank_data[$sale_name]['strong_a_ability_to_order_money'] = bcadd($sale_rank_data[$sale_name]['strong_a_ability_to_order_money'],
                            $to_order_money, 2);
                    }
                    // 强履约B
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_STRONG_B_ABILITY) {
                        $sale_rank_data[$sale_name]['strong_b_ability_to_order_num']++;
                        $sale_rank_data[$sale_name]['strong_b_ability_to_order_money'] = bcadd($sale_rank_data[$sale_name]['strong_b_ability_to_order_money'],
                            $to_order_money, 2);
                    }
                    // 弱履约A
                    if ($curr_quote_type == QuoteModel::QUOTE_TYPE_WEAK_ABILITY) {
                        $sale_rank_data[$sale_name]['weak_ability_to_order_num']++;
                        $sale_rank_data[$sale_name]['weak_ability_to_order_money'] = bcadd($sale_rank_data[$sale_name]['weak_ability_to_order_money'],
                            $to_order_money, 2);
                    }
                }
            }
        }

        $sale_rank_list = array_values($sale_rank_data);
        $collection = collect($sale_rank_list);
        $collection = $collection->sortByDesc('to_order_money');
        $sorted_sale_rank_list = array_values($collection->all());

        $file_path = "4month_sale_rank_report" . date("YmdHis") . ".xlsx";
        Excel::store(new FourMonthSaleRankExport($sorted_sale_rank_list), $file_path);

        echo "销售导出4月份销售业绩数据完成，file_path:{$file_path}\n";
        return 0;
    }


    // 获取初始化信息
    public static function getUserInitDefaultStatInfo($user_id = 0, $user_name = "", $department = '')
    {
        return [
            'user_id' => $user_id,
            'user_name' => $user_name,
            'department' => $department,
            'to_order_num' => 0,
            'to_order_money' => 0,
            'ability_to_order_num' => 0,
            'ability_to_order_money' => 0,
            'strong_a_ability_to_order_num' => 0,
            'strong_a_ability_to_order_money' => 0,
            'strong_b_ability_to_order_num' => 0,
            'strong_b_ability_to_order_money' => 0,
            'weak_ability_to_order_num' => 0,
            'weak_ability_to_order_money' => 0,
        ];
    }
}
