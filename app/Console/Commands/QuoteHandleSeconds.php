<?php

namespace App\Console\Commands;

use App\Http\Models\InquiryItemAssignModel;
use App\Http\Models\QuoteHandleStatisticsModel;
use App\Http\Models\QuoteModel;
use Illuminate\Console\Command;

class QuoteHandleSeconds extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'quote_handle_seconds';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '初始化采购报价的处理效率';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 指派表中 采购手动释放的报价单
        echo 'start_assign_time' . PHP_EOL;

        $assign_where = [
            ['close_type', '!=', InquiryItemAssignModel::CLOSE_TYPE_DEFAULT_EXPIRE],
            ['assign_status', '=', InquiryItemAssignModel::ASSIGN_STATUS_DEL],
            ['types', '=', 1],
            ['quote_id', '!=', 0], // 线上 08-25 开始有数据
        ];

        $assign_page = 0;
        $assign_limit = 50;
        do {
            $assign_page++;
            $assign_list = InquiryItemAssignModel::getPageList($assign_where, $assign_page, $assign_limit);
            $assign_last_page = $assign_list['last_page'];

            $assign_quote_ids = array_column($assign_list['data'], 'quote_id');
            $assign_quotes = QuoteModel::getListByIds($assign_quote_ids);
            $assign_quote_id_info_map = array_column($assign_quotes, null, 'id');

            $quote_handle_list = [];
            foreach($assign_list['data'] as $assign_item){
                
                if( isset($assign_quote_id_info_map[$assign_item['quote_id']]) ){
                    $assign_quote_info = $assign_quote_id_info_map[$assign_item['quote_id']];

                    $day_val = date('Y-m-d', $assign_quote_info['create_time']);
                    $month_val = date('m', $assign_quote_info['create_time']);
                    $quote_create_time = $assign_quote_info['create_time'];
                }else{
                    $day_val = date('Y-m-d', $assign_item['create_time']);
                    $month_val = date('m', $assign_item['create_time']);
                    $quote_create_time = $assign_item['create_time'];
                }

                $quote_handle_seconds = bcsub($assign_item['update_time'], $quote_create_time);

                $quote_handle_list[] = [
                    'buyer_uid' => $assign_item['assign_uid'],
                    'quote_id' => $assign_item['quote_id'],
                    'inquiry_items_id' => $assign_item['inquiry_items_id'],
                    'quote_handle_seconds' => $quote_handle_seconds,
                    'quote_handle_type' => 2,       // 1确认报价，2释放报价
                    'day_val' => $day_val,
                    'month_val' => $month_val,
                    'create_time' => time(),
                    'update_time' => time(),
                ];
            }
        
            QuoteHandleStatisticsModel::add($quote_handle_list);
        
            echo ' page: ' . $assign_page . ' | total_page: ' . $assign_last_page . PHP_EOL;
        } while ( $assign_page < $assign_last_page );
        echo 'end_assign_time' . PHP_EOL;


        // 采购确认的报价
        echo 'start_confirm_quote' . PHP_EOL;

        $quote_where = [
            ['source', '=', QuoteModel::SOURCE_RFQ],
            ['status', '=', QuoteModel::STATUS_OFFER],      // 已报价
            ['create_time', '>', strtotime('2022-01-01')],  
        ];

        $quote_page = 0;
        $quote_limit = 50;
        do {
            $quote_page++;
            $quote_list = QuoteModel::getListByWhere($quote_where, $quote_page, $quote_limit);
            $quote_last_page = $quote_list['last_page'];

            $quote_handle_list = [];
            foreach($quote_list['data'] as $quote_item){
                $quote_handle_seconds = bcsub($quote_item['update_time'], $quote_item['create_time']);

                $quote_handle_list[] = [
                    'buyer_uid' => $quote_item['create_uid'],
                    'quote_id' => $quote_item['id'],
                    'inquiry_items_id' => $quote_item['inquiry_items_id'],
                    'quote_handle_seconds' => $quote_handle_seconds,
                    'quote_handle_type' => 1,       // 1确认报价，2释放报价
                    'day_val' => date('Y-m-d', $quote_item['create_time']),
                    'month_val' => date('m', $quote_item['create_time']),
                    'create_time' => time(),
                    'update_time' => time(),
                ];
            }
        
            QuoteHandleStatisticsModel::add($quote_handle_list);
            echo ' page: ' . $quote_page . ' | total_page: ' . $quote_last_page . PHP_EOL;
        } while ( $quote_page < $quote_last_page );

        echo 'end_confirm_quote' . PHP_EOL;

        return 0;
    }
}
