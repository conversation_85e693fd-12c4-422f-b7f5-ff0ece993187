<?php

namespace App\Console\Commands;

use App\Http\Models\InquiryItemModel;
use Illuminate\Console\Command;

class TransferDeliverTimeForInquiryItems extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'command:transfer_deliver_time_for_inquiry_items';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $limit = 300;
        $start_id = 0;
        echo date("Y-m-d H:i:s") . "start to transfer_deliver_time_for_inquiry_items." . "\n";
        do{
            $deliver_time_array_standard = [];
            $inquiry_items_list = InquiryItemModel::listByStartId($start_id, $limit);
            if ($inquiry_items_list && is_array($inquiry_items_list)) {
                foreach ($inquiry_items_list as $param) {
                    if ($param['delivery_time'] == "0") {
                        $param = [
                            'id' => $param['id'],
                            'delivery_time' =>'',
                        ];
                        array_push($deliver_time_array_standard, $param);
                    } else if (!strpos($param['delivery_time'], '天')&& !empty($param['delivery_time'])) {
                        $param = [
                            'id' => $param['id'],
                            'delivery_time' =>$param['delivery_time'].'天',
                        ];
                        array_push($deliver_time_array_standard, $param);
                    } else {
                        $param = [
                            'id' => $param['id'],
                            'delivery_time' =>$param['delivery_time'],
                        ];
                        array_push($deliver_time_array_standard, $param);
                    }
                }
                foreach ($deliver_time_array_standard as $param){
                    InquiryItemModel::updateDeliverByWhere(['delivery_time' => $param['delivery_time']],
                        ['id' => $param['id']]);
                    echo "inquiryItemId:{$param['id']}'s delivery_time:{$param['delivery_time']}." . "\n";
                }
                $start_id = $inquiry_items_list[count($inquiry_items_list) - 1]['id'];
            }
            } while(count($inquiry_items_list) >= $limit);
        echo date("Y-m-d H:i:s") . "finish to transfer_deliver_time_for_inquiry_items." . "\n";
        return 0;
    }
}
