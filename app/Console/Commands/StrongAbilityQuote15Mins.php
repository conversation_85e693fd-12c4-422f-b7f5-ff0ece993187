<?php

namespace App\Console\Commands;

use App\Http\Models\FrqUserExtendInfoModel;
use App\Http\Models\InquiryItemModel;
use App\Http\Models\QuoteModel;
use App\Http\Queue\RabbitQueueModel;
use App\Http\Services\OrderService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class StrongAbilityQuote15Mins extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'string_ability_quote15mins';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '强履约报价，成本为4500的，如果15分钟没有转单，那么执行匹配复购报价';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        echo date("Y-m-d H:i:s") . "强履约报价，成本为4500的，如果15分钟没有转单，那么执行匹配复购报价功能已关闭." . "\n";
        return true;

        // 创建报价 15分钟 后
        $start_time = time() - 15 * 60;
        $end_time   = time() - 15 * 60 + 60;

        if ('local' == config('website.env')) {
            $start_time = time() - 2 * 60;
            $end_time   = time() - 2 * 60 + 60;
        }

        // 采购复购创建15分钟的
        $string_ability_quote_list = QuoteModel::getItemsByWhere([
            ['create_time', '>=', $start_time],
            ['create_time', '<=', $end_time],
        ], ['in_quote_types' => [QuoteModel::QUOTE_TYPE_STRONG_ABILITY, QuoteModel::QUOTE_TYPE_STRONG_B_ABILITY]], [
            'id', 'inquiry_items_id',
        ]);

        $inquiry_items_ids = array_column($string_ability_quote_list, 'inquiry_items_id');
        if (empty($inquiry_items_ids)) {
            echo date('Ymd H:i:s') . ' string_ability_quote 15Mins is empty ' . PHP_EOL;
            return 0;
        }

        echo date('Ymd H:i:s') . ' string_ability_quote 15Mins: ' . join(',', $inquiry_items_ids) . PHP_EOL;

        $inquiry_items_ids  = array_values(array_unique($inquiry_items_ids));
        $inquiry_items_list = InquiryItemModel::getInquiryItemsByIds($inquiry_items_ids);
        if ($inquiry_items_list) {
            $inquiry_items_map = array_column($inquiry_items_list, null, 'id');
            // 查询询价单关联销售单
            $inquiry_rela_ordersn_map = OrderService::getInquiryItemIdRelaOrderSnsMap($inquiry_items_ids);
            $RabbitQueueModel         = new RabbitQueueModel();
            foreach ($inquiry_items_ids as $inquiry_items_id) {
                $inquiry_item_info = $inquiry_items_map[$inquiry_items_id];
                // 如果强履约的询价单，15分钟没有转单，那么检测是否已执行采购复购和品牌pm，
                // 如果有，说明报价高于4500，已经执行过采购复购和品牌pm匹配。
                // 如果没有，说明报价低于4500，未执行采购复购和品牌pm匹配，同时15分钟未转单，需要重新执行分配
                if (!isset($inquiry_rela_ordersn_map[$inquiry_items_id])) {
                    $repurchase_pm_quote_list = QuoteModel::getItemsByWhere([
                        ['inquiry_items_id', '=', $inquiry_items_id],
                    ], [
                        'in_quote_types' => [QuoteModel::QUOTE_TYPE_REPURCHASE, QuoteModel::QUOTE_TYPE_PM],
                    ], ['id']);
                    if (empty($repurchase_pm_quote_list)) {
                        // 采购复购
                        $RabbitQueueModel->insertQueue('/queue/quote/autoQuoteByRepurchase', [
                            "inquiry_items_id" => $inquiry_items_id,
                        ], $RabbitQueueModel::QUEUE_FRQ);
                        echo date('Ymd H:i:s') . "inquiry_items_id:{$inquiry_items_id}强履约匹配未转单，未执行采购复购和品牌pm匹配，流转采购复购报价，goods_name:{$inquiry_item_info['goods_name']},brand_name:{$inquiry_item_info['brand_name']}" . PHP_EOL;
                    } else {
                        echo date('Ymd H:i:s') . "inquiry_items_id:{$inquiry_items_id}强履约匹配未转单，已执行采购复购和品牌pm匹配，不做处理，goods_name:{$inquiry_item_info['goods_name']},brand_name:{$inquiry_item_info['brand_name']}" . PHP_EOL;
                    }
                } else {
                    echo date('Ymd H:i:s') . "inquiry_items_id:{$inquiry_items_id}已转单，goods_name:{$inquiry_item_info['goods_name']},brand_name:{$inquiry_item_info['brand_name']}" . PHP_EOL;
                }
            }
        }
        return 0;
    }
}
